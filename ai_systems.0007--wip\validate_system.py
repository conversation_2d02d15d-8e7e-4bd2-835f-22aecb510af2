#!/usr/bin/env python3
"""
System validation script for AI Systems 0007.

Performs comprehensive validation of the template system including:
- Template generation
- Catalog creation
- Sequence execution
- Progressive compression validation
"""

import subprocess
import sys
import json
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"VALIDATING: {description}")
    print(f"COMMAND: {' '.join(cmd)}")
    print('='*60)
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ SUCCESS")
        if result.stdout.strip():
            print("OUTPUT:")
            print(result.stdout)
    else:
        print("❌ FAILED")
        print("STDERR:")
        print(result.stderr)
        if result.stdout.strip():
            print("STDOUT:")
            print(result.stdout)
    
    return result.returncode == 0


def validate_progressive_compression():
    """Validate that progressive compression is working correctly."""
    print(f"\n{'='*60}")
    print("VALIDATING: Progressive Compression Pattern")
    print('='*60)
    
    # Check if we have sequences available
    result = subprocess.run(
        [sys.executable, "src/lvl1_sequence_executor.py", "--list-sequences"],
        capture_output=True, text=True
    )
    
    if result.returncode != 0:
        print("❌ Could not list sequences")
        return False
    
    if "1200" not in result.stdout:
        print("❌ Sequence 1200 not found")
        return False
    
    print("✅ Sequences available")
    
    # Test progressive compression by examining template files
    md_dir = Path("src/templates/stage1/md")
    if not md_dir.exists():
        print("❌ Template directory not found")
        return False
    
    # Check for progressive compression in template content
    compression_steps = ["a", "b", "c", "d"]
    step_descriptions = {
        "a": "comprehensive",
        "b": "distill",
        "c": "compress", 
        "d": "essence"
    }
    
    for step in compression_steps:
        template_file = md_dir / f"1200-{step}-runway_prompt_generator1.md"
        if not template_file.exists():
            print(f"❌ Template file {template_file.name} not found")
            return False
        
        content = template_file.read_text()
        expected_keyword = step_descriptions[step]
        
        if expected_keyword in content.lower():
            print(f"✅ Step {step}: Contains '{expected_keyword}' keyword")
        else:
            print(f"⚠️  Step {step}: Missing '{expected_keyword}' keyword (but file exists)")
    
    return True


def validate_catalog_structure():
    """Validate catalog structure and content."""
    print(f"\n{'='*60}")
    print("VALIDATING: Catalog Structure")
    print('='*60)
    
    catalog_path = Path("src/templates/lvl1.md.templates.json")
    if not catalog_path.exists():
        print("❌ Catalog file not found")
        return False
    
    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in catalog: {e}")
        return False
    
    # Check required sections
    required_sections = ["catalog_meta", "templates", "sequences"]
    for section in required_sections:
        if section not in catalog:
            print(f"❌ Missing section: {section}")
            return False
        print(f"✅ Section present: {section}")
    
    # Check metadata
    meta = catalog["catalog_meta"]
    if meta.get("level") != "lvl1":
        print(f"❌ Wrong level: {meta.get('level')}")
        return False
    
    if meta.get("format") != "md":
        print(f"❌ Wrong format: {meta.get('format')}")
        return False
    
    print(f"✅ Catalog metadata valid")
    print(f"   - Level: {meta['level']}")
    print(f"   - Format: {meta['format']}")
    print(f"   - Templates: {meta['total_templates']}")
    print(f"   - Sequences: {meta['total_sequences']}")
    
    # Check template structure
    templates = catalog["templates"]
    if not templates:
        print("❌ No templates found")
        return False
    
    # Validate a sample template
    sample_id = list(templates.keys())[0]
    sample_template = templates[sample_id]
    
    required_parts = ["title", "interpretation", "transformation"]
    if "parts" not in sample_template:
        print(f"❌ Template {sample_id} missing 'parts'")
        return False
    
    parts = sample_template["parts"]
    for part in required_parts:
        if part not in parts:
            print(f"❌ Template {sample_id} missing part: {part}")
            return False
    
    print(f"✅ Template structure valid (checked {sample_id})")
    
    return True


def main():
    """Run complete system validation."""
    print("AI SYSTEMS 0007 - SYSTEM VALIDATION")
    print("="*60)
    
    validation_steps = [
        # Basic functionality tests
        (
            [sys.executable, "run_tests.py"],
            "Unit and Integration Tests"
        ),
        
        # Template generation
        (
            [sys.executable, "src/templates/stage1/generators/1200-1299.generators.new_wip_autoid.py"],
            "Template Generation"
        ),
        
        # Catalog generation
        (
            [sys.executable, "src/templates/lvl1_md_to_json.py"],
            "Catalog Generation"
        ),
        
        # System functionality
        (
            [sys.executable, "src/lvl1_sequence_executor.py", "--list-sequences"],
            "Sequence Listing"
        ),
        
        (
            [sys.executable, "src/lvl1_sequence_executor.py", "--list-models"],
            "Model Listing"
        ),
        
        (
            [sys.executable, "src/lvl1_sequence_executor.py", "--help"],
            "Help System"
        ),
    ]
    
    # Run validation steps
    all_passed = True
    for cmd, description in validation_steps:
        if not run_command(cmd, description):
            all_passed = False
    
    # Custom validation functions
    if not validate_progressive_compression():
        all_passed = False
    
    if not validate_catalog_structure():
        all_passed = False
    
    # Final summary
    print(f"\n{'='*60}")
    print("VALIDATION SUMMARY")
    print('='*60)
    
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED")
        print("\nThe AI Systems 0007 template system is fully functional:")
        print("✅ Template generation working")
        print("✅ Progressive compression pattern implemented")
        print("✅ Catalog system operational")
        print("✅ Sequence execution ready")
        print("✅ Stage-based organization active")
        print("✅ Auto-ID system functional")
        print("\nSystem is ready for production use!")
        return True
    else:
        print("❌ SOME VALIDATIONS FAILED")
        print("\nPlease review the errors above and fix issues before proceeding.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
