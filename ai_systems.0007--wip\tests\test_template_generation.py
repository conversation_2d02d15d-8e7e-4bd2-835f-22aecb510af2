#!/usr/bin/env python3
"""
Test suite for template generation functionality.

Tests the core template generation system including:
- Auto-ID assignment
- Progressive compression patterns
- Stage-based organization
- Template validation
"""

import unittest
import tempfile
import shutil
import os
import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from templates.lvl1_md_to_json import TemplateConfig, generate_catalog, TemplateConfigMD


class TestTemplateGeneration(unittest.TestCase):
    """Test template generation and catalog creation."""
    
    def setUp(self):
        """Set up test environment with temporary directories."""
        self.test_dir = tempfile.mkdtemp()
        self.templates_dir = Path(self.test_dir) / "templates"
        self.templates_dir.mkdir()
        
        # Create sample template files
        self.sample_templates = {
            "1200-a-test_sequence.md": "[Test Template A] Your goal is not to **describe** but to **transform**. Execute as: `{role=test_transformer; input=[data:str]; process=[analyze(), transform()]; output={result:str}}`",
            "1200-b-test_sequence.md": "[Test Template B] Your goal is not to **elaborate** but to **distill**. Execute as: `{role=test_distiller; input=[data:str]; process=[distill()]; output={result:str}}`",
            "1200-c-test_sequence.md": "[Test Template C] Your goal is not to **expand** but to **compress**. Execute as: `{role=test_compressor; input=[data:str]; process=[compress()]; output={result:str}}`",
            "1200-d-test_sequence.md": "[Test Template D] Your goal is not to **modify** but to **essence**. Execute as: `{role=test_essencer; input=[data:str]; process=[essence()]; output={result:str}}`"
        }
        
        for filename, content in self.sample_templates.items():
            (self.templates_dir / filename).write_text(content, encoding='utf-8')
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def test_stage_configuration(self):
        """Test stage configuration constants."""
        stages = TemplateConfig.STAGES
        
        # Test stage1 configuration
        self.assertIn("stage1", stages)
        stage1 = stages["stage1"]
        self.assertEqual(stage1["range"], (1000, 1999))
        self.assertEqual(stage1["description"], "Prototyping/Testing")
        self.assertTrue(stage1["auto_id"])
        
        # Test stage2 configuration
        self.assertIn("stage2", stages)
        stage2 = stages["stage2"]
        self.assertEqual(stage2["range"], (2000, 2999))
        self.assertEqual(stage2["description"], "Validated/Unplaced")
        self.assertFalse(stage2["auto_id"])
        
        # Test stage3 configuration
        self.assertIn("stage3", stages)
        stage3 = stages["stage3"]
        self.assertEqual(stage3["range"], (3000, 3999))
        self.assertEqual(stage3["description"], "Finalized/Production")
        self.assertFalse(stage3["auto_id"])
    
    def test_template_id_stage_detection(self):
        """Test stage detection from template IDs."""
        test_cases = [
            ("1200-a-test", "stage1"),
            ("2500-b-test", "stage2"),
            ("3100-c-test", "stage3"),
            ("500-d-test", "legacy"),
            ("invalid-id", "unknown")
        ]
        
        for template_id, expected_stage in test_cases:
            stage_name, stage_info = TemplateConfig.get_stage_for_id(template_id)
            self.assertEqual(stage_name, expected_stage, 
                           f"Template ID {template_id} should be in {expected_stage}")
    
    def test_catalog_generation(self):
        """Test catalog generation from template files."""
        # Create a temporary config for testing
        class TestConfig(TemplateConfigMD):
            SOURCE_DIR = str(self.templates_dir)
        
        catalog = generate_catalog(TestConfig, self.test_dir)
        
        # Test catalog structure
        self.assertIn("catalog_meta", catalog)
        self.assertIn("templates", catalog)
        self.assertIn("sequences", catalog)
        
        # Test metadata
        meta = catalog["catalog_meta"]
        self.assertEqual(meta["level"], "lvl1")
        self.assertEqual(meta["format"], "md")
        self.assertEqual(meta["total_templates"], 4)
        self.assertEqual(meta["total_sequences"], 1)
        
        # Test templates
        templates = catalog["templates"]
        self.assertEqual(len(templates), 4)
        
        # Test sequence
        sequences = catalog["sequences"]
        self.assertIn("1200", sequences)
        sequence = sequences["1200"]
        self.assertEqual(len(sequence), 4)
        
        # Test sequence ordering
        steps = [step["step"] for step in sequence]
        self.assertEqual(steps, ["a", "b", "c", "d"])
    
    def test_progressive_compression_pattern(self):
        """Test that templates follow progressive compression pattern."""
        # Create a temporary config for testing
        class TestConfig(TemplateConfigMD):
            SOURCE_DIR = str(self.templates_dir)
        
        catalog = generate_catalog(TestConfig, self.test_dir)
        templates = catalog["templates"]
        
        # Test that each step has appropriate compression keywords
        compression_keywords = {
            "a": ["transform", "analyze"],
            "b": ["distill"],
            "c": ["compress"],
            "d": ["essence"]
        }
        
        for step, expected_keywords in compression_keywords.items():
            template_id = f"1200-{step}-test_sequence"
            self.assertIn(template_id, templates)
            
            template = templates[template_id]
            interpretation = template["parts"]["interpretation"]
            
            # Check that at least one expected keyword is present
            found_keyword = any(keyword in interpretation.lower() 
                              for keyword in expected_keywords)
            self.assertTrue(found_keyword, 
                          f"Step {step} should contain compression keywords: {expected_keywords}")
    
    def test_template_structure_validation(self):
        """Test that templates follow the canonical three-part structure."""
        # Create a temporary config for testing
        class TestConfig(TemplateConfigMD):
            SOURCE_DIR = str(self.templates_dir)
        
        catalog = generate_catalog(TestConfig, self.test_dir)
        templates = catalog["templates"]
        
        for template_id, template_data in templates.items():
            with self.subTest(template_id=template_id):
                # Test that all required parts are present
                self.assertIn("parts", template_data)
                parts = template_data["parts"]
                
                self.assertIn("title", parts)
                self.assertIn("interpretation", parts)
                self.assertIn("transformation", parts)
                
                # Test that parts are not empty
                self.assertTrue(parts["title"].strip())
                self.assertTrue(parts["interpretation"].strip())
                self.assertTrue(parts["transformation"].strip())
                
                # Test goal negation pattern in interpretation
                interpretation = parts["interpretation"]
                self.assertIn("Your goal is not to", interpretation)
                self.assertIn("but to", interpretation)
                
                # Test transformation block format
                transformation = parts["transformation"]
                self.assertTrue(transformation.startswith("`{"))
                self.assertTrue(transformation.endswith("}`"))


if __name__ == "__main__":
    unittest.main()
