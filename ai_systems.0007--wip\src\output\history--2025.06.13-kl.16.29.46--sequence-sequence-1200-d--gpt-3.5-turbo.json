  {
  "initial_prompt": "A dragon breathing fire",
  "sequence_id": "1200:d",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "A dragon breathing fire",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"core_prompt\": \"A fire-breathing dragon\"\n}",
        }
      }
    }