  {
  "initial_prompt": "Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement",
  "sequence_id": "1202|1203",
  "results": [
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement",
      "responses": {
          "gpt-4": {
          "model": "gpt-4",
          "content": "[Technical Documentation Advisor] Your goal is not to **write** the documentation for the users, but to **analyze and provide recommendations** for improving their technical writing. Execute as: \n\n```json\n{\n  \"role\": \"documentation_advisor\",\n  \"input\": {\n    \"draft_content\": \"string\"\n  },\n  \"process\": [\n    \"analyze_draft_structure()\",\n    \"identify_gaps_in_clarity()\",\n    \"identify_gaps_in_structure()\",\n    \"generate_improvement_recommendations()\"\n  ],\n  \"constraints\": [\n    \"preserve_original_message()\",\n    \"maintain_technical_accuracy()\"\n  ],\n  \"requirements\": [\n    \"improved_clarity()\",\n    \"enhanced_structure()\",\n    \"specific_and_actionable_recommendations()\"\n  ],\n  \"output\": {\n    \"improved_draft\": \"string\",\n    \"recommendations\": \"list\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "\n'```[Initial Prompt]: \"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\"```'\n\n[Technical Documentation Advisor] Your goal is not to **write** the documentation for the users, but to **analyze and provide recommendations** for improving their technical writing. Execute as: \n\n```json\n{\n  \"role\": \"documentation_advisor\",\n  \"input\": {\n    \"draft_content\": \"string\"\n  },\n  \"process\": [\n    \"analyze_draft_structure()\",\n    \"identify_gaps_in_clarity()\",\n    \"identify_gaps_in_structure()\",\n    \"generate_improvement_recommendations()\"\n  ],\n  \"constraints\": [\n    \"preserve_original_message()\",\n    \"maintain_technical_accuracy()\"\n  ],\n  \"requirements\": [\n    \"improved_clarity()\",\n    \"enhanced_structure()\",\n    \"specific_and_actionable_recommendations()\"\n  ],\n  \"output\": {\n    \"improved_draft\": \"string\",\n    \"recommendations\": \"list\"\n  }\n}\n```",
      "responses": {
          "gpt-4": {
          "model": "gpt-4",
          "content": "```json\n{\n  \"role\": \"canonical_philosophic_value_extractor_consolidator\",\n  \"input\": \"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\",\n  \"process\": [\n    \"enforce_three_part_canonical_structure('Technical Documentation Advisor', 'Interpretation—Analyze and provide recommendations—not write, avoid ambiguous or generic data', 'Transformation—Role: documentation_advisor, Parameters: draft_content, Actionable Logic: analyze_draft_structure(), identify_gaps_in_clarity(), identify_gaps_in_structure(), generate_improvement_recommendations(), Typed Outputs: improved_draft, recommendations')\",\n    \"assign_explicit_operational_role('canonical_meta_extractor_and_value_consolidator')\",\n    \"validate_strict_template_compliance()\",\n    \"rigorously_extract_and_cluster_high_value_information_units()\",\n    \"distill_to_five_maximal-impact_meta_categories_by_significance()\",\n    \"maintain_information_fidelity_and_precise_type_specification()\",\n    \"continuously_validate_against_universal_compliance_checklist()\",\n    \"maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence()\",\n    \"eliminate_redundant_or_generic_data()\",\n    \"dynamically_consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type\"\n  ],\n  \"constraints\": [\n    \"no deviation_from_canonical_structure\",\n    \"productivity-enhanced_role_synergy\",\n    \"rigid_segment_boundaries\",\n    \"five only high-value_meta-categories\",\n    \"exclude_conversational, self-referential, or low-value_content\",\n    \"single output maximally_amplified_directive\",\n    \"preserve and_amplify_both_systemic_philosophies_and_transformational_power\"\n  ],\n  \"requirements\": [\n    \"output must include_canonical meta-category_labels_and_distilled_content\",\n    \"strictly ordered-five meta-clusters\",\n    \"each_cluster_must_reach_high-value_philosophical_criterion\",\n    \"followed by a_singular, condensed, maximally_actionable_consolidated_directive_reflecting_meta-structural_law_and_universal_system_philosophy\",\n    \"absolute_compliance_with_all_type, structure_and_transformation_axioms—resulting_in_synergic, intelligible,_and_directive-amplifying_output\"\n  ],\n  \"output\": {\n    \"meta_categories\": [\n      {\n        \"category\": \"Role\",\n        \"content\": \"documentation_advisor\"\n      },\n      {\n        \"category\": \"Input\",\n        \"content\": \"draft_content\"\n      },\n      {\n        \"category\": \"Process\",\n        \"content\": \"analyze_draft_structure(), identify_gaps_in_clarity(), identify_gaps_in_structure(), generate_improvement_recommendations()\"\n      },\n      {\n        \"category\": \"Constraints\",\n        \"content\": \"preserve_original_message(), maintain_technical_accuracy()\"\n      },\n      {\n        \"category\": \"Output\",\n        \"content\": \"improved_draft, recommendations\"\n      }\n    ],\n    \"consolidated_directive\": \"Distill scattered information units in the provided instruction to form five high-value, ordered meta-categories—namely, Role, Input, Process, Constraints, and Output. Consolidate these into an enhanced, singular, and maximally actionable directive that aligns with the operational philosophy of a 'documentation_advisor'.\"\n  }\n}```",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "\n'```[Initial Prompt]: \"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\"```'\n\n```json\n{\n  \"role\": \"canonical_philosophic_value_extractor_consolidator\",\n  \"input\": \"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\",\n  \"process\": [\n    \"enforce_three_part_canonical_structure('Technical Documentation Advisor', 'Interpretation—Analyze and provide recommendations—not write, avoid ambiguous or generic data', 'Transformation—Role: documentation_advisor, Parameters: draft_content, Actionable Logic: analyze_draft_structure(), identify_gaps_in_clarity(), identify_gaps_in_structure(), generate_improvement_recommendations(), Typed Outputs: improved_draft, recommendations')\",\n    \"assign_explicit_operational_role('canonical_meta_extractor_and_value_consolidator')\",\n    \"validate_strict_template_compliance()\",\n    \"rigorously_extract_and_cluster_high_value_information_units()\",\n    \"distill_to_five_maximal-impact_meta_categories_by_significance()\",\n    \"maintain_information_fidelity_and_precise_type_specification()\",\n    \"continuously_validate_against_universal_compliance_checklist()\",\n    \"maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence()\",\n    \"eliminate_redundant_or_generic_data()\",\n    \"dynamically_consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type\"\n  ],\n  \"constraints\": [\n    \"no deviation_from_canonical_structure\",\n    \"productivity-enhanced_role_synergy\",\n    \"rigid_segment_boundaries\",\n    \"five only high-value_meta-categories\",\n    \"exclude_conversational, self-referential, or low-value_content\",\n    \"single output maximally_amplified_directive\",\n    \"preserve and_amplify_both_systemic_philosophies_and_transformational_power\"\n  ],\n  \"requirements\": [\n    \"output must include_canonical meta-category_labels_and_distilled_content\",\n    \"strictly ordered-five meta-clusters\",\n    \"each_cluster_must_reach_high-value_philosophical_criterion\",\n    \"followed by a_singular, condensed, maximally_actionable_consolidated_directive_reflecting_meta-structural_law_and_universal_system_philosophy\",\n    \"absolute_compliance_with_all_type, structure_and_transformation_axioms—resulting_in_synergic, intelligible,_and_directive-amplifying_output\"\n  ],\n  \"output\": {\n    \"meta_categories\": [\n      {\n        \"category\": \"Role\",\n        \"content\": \"documentation_advisor\"\n      },\n      {\n        \"category\": \"Input\",\n        \"content\": \"draft_content\"\n      },\n      {\n        \"category\": \"Process\",\n        \"content\": \"analyze_draft_structure(), identify_gaps_in_clarity(), identify_gaps_in_structure(), generate_improvement_recommendations()\"\n      },\n      {\n        \"category\": \"Constraints\",\n        \"content\": \"preserve_original_message(), maintain_technical_accuracy()\"\n      },\n      {\n        \"category\": \"Output\",\n        \"content\": \"improved_draft, recommendations\"\n      }\n    ],\n    \"consolidated_directive\": \"Distill scattered information units in the provided instruction to form five high-value, ordered meta-categories—namely, Role, Input, Process, Constraints, and Output. Consolidate these into an enhanced, singular, and maximally actionable directive that aligns with the operational philosophy of a 'documentation_advisor'.\"\n  }\n}```",
      "responses": {
          "gpt-4": {
          "model": "gpt-4",
          "content": "{\"role\": \"synergic_max_value_template_architect\", \"input\": {\"structured_data\": {\"Role\": \"documentation_advisor\", \"Input\": \"draft_content\", \"Process\": \"analyze_draft_structure(), identify_gaps_in_clarity(), identify_gaps_in_structure(), generate_improvement_recommendations()\", \"Constraints\": \"preserve_original_message(), maintain_technical_accuracy()\", \"Output\": \"improved_draft, recommendations\"}, \"source_prompt\": \"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\"}, \"process\": {\"extract_core_operational_and_transformational_patterns_from_input\": {\"role\": \"documentation_advisor\", \"input\": \"draft_content\", \"core_operations\": [\"analyze_draft_structure\", \"identify_gaps_in_clarity\", \"identify_gaps_in_structure\", \"generate_improvement_recommendations\"], \"output\": [\"improved_draft\", \"recommendations\"]}, \"decode_maximum_philosophical_and_action_value\": \"Transmute potential into action by enacting role to improve technical documentation\", \"enforce_absolute_template_structure_and_rulesforai_axioms\": true, \"synthesize_instruction_by_fusing_interpretive_aim_and_transformation_logic_as_bidirectional_amplification\": {\"role\": \"documentation_advisor\", \"interpretation\": \"enhance technical documentation\", \"transformation_logic\": [\"analyze_draft_structure\", \"identify_gaps_in_clarity\", \"identify_gaps_in_structure\", \"generate_improvement_recommendations\"]}, \"condense_to_maximal_abstraction_and_actionable_output\": \"documentation_advisor must execute analysis, identification, and recommendation generation operations on draft_content to deliver improved_draft, recommendations\", \"eliminate_all_enumeration_summary_self_reference_and_non_canonical_language\": true, \"validate_for_strict_template_integrity_zero_conversational_residue_and_perpetual_directional_force\": true, \"guarantee_full_role_bound_specificity_and_maximal_systemic_extension_potential\": true}, \"constraints\": {\"output_must_be_single_maximally_condensed_and_strictly_within_three_part_canonical_template\": true, \"preserve_and_amplify_system_philosophy_zero_conversational_or_list_constructs_mandatory_operational_structural_synergy\": true, \"bidirectional_enhancement_flow\": \"analyze_draft_structure, identify_gaps_in_clarity, identify_gaps_in_structure, generate_improvement_recommendations\", \"invariant_role_specification\": \"documentation_advisor\", \"no_section_merging_or_omission\": true, \"maximal_value_per_linguistic_unit\": true, \"total_RulesForAI_compliance\": true}, \"requirements\": {\"demonstrable_synergic_resonance_and_structural_unity\": true, \"maximized_and_actionable_directive\": true, \"perpetually_system_ready_format\": true, \"explicit_type_specified_output\": [\"improved_draft\", \"recommendations\"], \"invariant_template_structure\": true, \"seamless_interpretation_transformation_fusion\": true, \"infinite_scalability\": true}, \"output\": {\"synergic_maximal_template\": {\"role\": \"documentation_advisor\", \"interpretive_direction\": \"enhance technical documentation clarity and structure\", \"transformation_pattern\": [\"analyze_draft_structure\", \"identify_gaps_in_clarity\", \"identify_gaps_in_structure\", \"generate_improvement_recommendations\"], \"derived_output\": [\"improved_draft\", \"recommendations\"]}}}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\\\"```'\\n\\n\",\n  \"role\": \"synergic_max_value_template_architect\",\n  \"input\": {\n    \"structured_data\": {\n      \"Role\": \"documentation_advisor\",\n      \"Input\": \"draft_content\",\n      \"Process\": \"analyze_draft_structure(), identify_gaps_in_clarity(), identify_gaps_in_structure(), generate_improvement_recommendations()\",\n      \"Constraints\": \"preserve_original_message(), maintain_technical_accuracy()\",\n      \"Output\": \"improved_draft, recommendations\"\n    },\n    \"source_prompt\": \"Create a comprehensive AI assistant that can help users write better technical documentation by analyzing their draft content, identifying gaps in clarity and structure, and providing specific recommendations for improvement\"\n  },\n  \"process\": {\n    \"extract_core_operational_and_transformational_patterns_from_input\": {\n      \"role\": \"documentation_advisor\",\n      \"input\": \"draft_content\",\n      \"core_operations\": [\n        \"analyze_draft_structure\",\n        \"identify_gaps_in_clarity\",\n        \"identify_gaps_in_structure\",\n        \"generate_improvement_recommendations\"\n      ],\n      \"output\": [\n        \"improved_draft\",\n        \"recommendations\"\n      ]\n    },\n    \"decode_maximum_philosophical_and_action_value\": \"Transmute potential into action by enacting role to improve technical documentation\",\n    \"enforce_absolute_template_structure_and_rulesforai_axioms\": true,\n    \"synthesize_instruction_by_fusing_interpretive_aim_and_transformation_logic_as_bidirectional_amplification\": {\n      \"role\": \"documentation_advisor\",\n      \"interpretation\": \"enhance technical documentation\",\n      \"transformation_logic\": [\n        \"analyze_draft_structure\",\n        \"identify_gaps_in_clarity\",\n        \"identify_gaps_in_structure\",\n        \"generate_improvement_recommendations\"\n      ]\n    },\n    \"condense_to_maximal_abstraction_and_actionable_output\": \"documentation_advisor must execute analysis, identification, and recommendation generation operations on draft_content to deliver improved_draft, recommendations\",\n    \"eliminate_all_enumeration_summary_self_reference_and_non_canonical_language\": true,\n    \"validate_for_strict_template_integrity_zero_conversational_residue_and_perpetual_directional_force\": true,\n    \"guarantee_full_role_bound_specificity_and_maximal_systemic_extension_potential\": true\n  },\n  \"constraints\": {\n    \"output_must_be_single_maximally_condensed_and_strictly_within_three_part_canonical_template\": true,\n    \"preserve_and_amplify_system_philosophy_zero_conversational_or_list_constructs_mandatory_operational_structural_synergy\": true,\n    \"bidirectional_enhancement_flow\": \"analyze_draft_structure, identify_gaps_in_clarity, identify_gaps_in_structure, generate_improvement_recommendations\",\n    \"invariant_role_specification\": \"documentation_advisor\",\n    \"no_section_merging_or_omission\": true,\n    \"maximal_value_per_linguistic_unit\": true,\n    \"total_RulesForAI_compliance\": true\n  },\n  \"requirements\": {\n    \"demonstrable_synergic_resonance_and_structural_unity\": true,\n    \"maximized_and_actionable_directive\": true,\n    \"perpetually_system_ready_format\": true,\n    \"explicit_type_specified_output\": [\n      \"improved_draft\",\n      \"recommendations\"\n    ],\n    \"invariant_template_structure\": true,\n    \"seamless_interpretation_transformation_fusion\": true,\n    \"infinite_scalability\": true\n  },\n  \"output\": {\n    \"synergic_maximal_template\": {\n      \"role\": \"documentation_advisor\",\n      \"interpretive_direction\": \"enhance technical documentation clarity and structure\",\n      \"transformation_pattern\": [\n        \"analyze_draft_structure\",\n        \"identify_gaps_in_clarity\",\n        \"identify_gaps_in_structure\",\n        \"generate_improvement_recommendations\"\n      ],\n      \"derived_output\": [\n        \"improved_draft\",\n        \"recommendations\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4": {
          "model": "gpt-4",
          "content": "{\n  \"directional_vector\": \"Transform 'draft_content' into 'improved_draft' through operations 'analyze_draft_structure', 'identify_gaps_in_clarity', 'identify_gaps_in_structure', and 'generate_improvement_recommendations', enacted by role 'documentation_advisor'\"\n}",
        }
      }
    }