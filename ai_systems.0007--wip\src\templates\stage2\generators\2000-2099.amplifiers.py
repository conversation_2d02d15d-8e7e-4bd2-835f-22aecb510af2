#!/usr/bin/env python3

"""
Stage 2 Amplifiers Generator
2000-2199: Amplification and Enhancement Templates

Stage 2 templates are validated but unplaced - manual ID assignment required.
Auto-ID is disabled for this stage to ensure deliberate placement.
"""

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import TemplateConfig

OUTPUT_DIR = Path(__file__).parent.parent / "md"

# Get stage configuration for this generator
CURRENT_STAGE = "stage2"
STAGE_CONFIG = TemplateConfig.STAGES[CURRENT_STAGE]
STAGE_RANGE = STAGE_CONFIG["range"]
AUTO_ID_ENABLED = STAGE_CONFIG["auto_id"]

TEMPLATES = {
    # Manual ID Templates (Stage 2): Auto-ID is disabled
    # Must specify exact ID in format: "2001-a-template_name"

    # This will trigger a warning (auto-ID format in non-auto-ID stage)
    "a-test_amplifier": {
        "title": "Test Amplifier",
        "interpretation": "This template will trigger a warning because auto-ID is disabled. Execute as:",
        "transformation": "`{role=test; output={warning:str}}`"
    },

    "2001-a-content_amplifier": {
        "title": "Content Amplifier",
        "interpretation": "Your goal is not to **summarize** the content, but to **amplify** it by expanding key points, adding relevant details, and enhancing depth while maintaining focus. Execute as:",
        "transformation": "`{role=comprehensive_content_amplifier; input=[content:any]; process=[identify_key_points(), expand_core_concepts(), add_relevant_details(), enhance_depth(), maintain_focus(), preserve_coherence()]; constraints=[expand_meaningfully(), avoid_redundancy(), maintain_quality()]; requirements=[enhanced_depth(), expanded_coverage(), improved_richness()]; output={amplified_content:str}}`"
    },
    
    "2001-b-content_amplifier": {
        "title": "Content Amplifier",
        "interpretation": "Your goal is not to **repeat** the content, but to **amplify** its key elements with focused expansion. Execute as:",
        "transformation": "`{role=focused_content_amplifier; input=[content:any]; process=[identify_primary_points(), expand_key_elements(), add_supporting_details()]; constraints=[focus_on_main_amplifications(), maintain_coherence()]; requirements=[enhanced_key_points(), focused_expansion()]; output={amplified_content:str}}`"
    },
    
    "2001-c-content_amplifier": {
        "title": "Content Amplifier",
        "interpretation": "Your goal is not to **alter** the meaning, but to **amplify** essential elements. Execute as:",
        "transformation": "`{role=essential_content_amplifier; input=[content:any]; process=[expand_core_elements(), add_essential_details()]; constraints=[essential_amplification_only()]; requirements=[enhanced_core_content()]; output={amplified_content:str}}`"
    },
    
    "2001-d-content_amplifier": {
        "title": "Content Amplifier",
        "interpretation": "Your goal is not to **minimize** but to **enhance** core value. Execute as:",
        "transformation": "`{role=core_amplifier; input=[content:any]; process=[enhance_core_value()]; output={enhanced_content:str}}`"
    },
}

def create_template_files():
    """Generate markdown template files with stage-aware processing."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []
    
    # Check if auto-ID is enabled for this stage
    if not AUTO_ID_ENABLED:
        print(f"INFO: Auto-ID is disabled for {CURRENT_STAGE} - using manual IDs only")
    
    # Separate templates by ID type
    auto_id_templates = {}
    manual_id_templates = {}
    
    for template_key, template in TEMPLATES.items():
        if template_key.startswith(('a-', 'b-', 'c-', 'd-', 'e-', 'f-', 'g-', 'h-')):
            # Auto-ID template (starts with letter-dash)
            if AUTO_ID_ENABLED:
                sequence_name = template_key.split('-', 1)[1]
                if sequence_name not in auto_id_templates:
                    auto_id_templates[sequence_name] = {}
                auto_id_templates[sequence_name][template_key] = template
            else:
                print(f"WARNING: Auto-ID template '{template_key}' found but auto-ID is disabled for {CURRENT_STAGE}")
                print(f"         Please use manual ID format: 'XXXX-{template_key}' where XXXX is in range {STAGE_RANGE[0]}-{STAGE_RANGE[1]}")
                continue
        else:
            # Manual ID template (already has numeric ID)
            # Validate ID is in correct stage range
            try:
                id_num = int(template_key.split('-')[0])
                if not (STAGE_RANGE[0] <= id_num <= STAGE_RANGE[1]):
                    print(f"WARNING: Template '{template_key}' ID {id_num} is outside {CURRENT_STAGE} range ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
                    continue
            except (ValueError, IndexError):
                print(f"WARNING: Invalid template key format: '{template_key}'")
                continue
                
            manual_id_templates[template_key] = template
    
    # Process manual ID templates
    for filename, template in manual_id_templates.items():
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
            
        created_files.append(f"{filename}.md")
    
    return created_files

def main():
    """Main execution function."""
    import sys
    
    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except Exception:
            pass
    
    # Display stage configuration
    print(f"STAGE: {CURRENT_STAGE.upper()} ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
    print(f"AUTO-ID: {'Enabled' if AUTO_ID_ENABLED else 'Disabled'}")
    print(f"DESCRIPTION: {STAGE_CONFIG['description']}")
    print()
    
    created_files = create_template_files()
    
    print(f"SUCCESS: Created {CURRENT_STAGE} amplifier templates:")
    for file in created_files:
        print(f"   - {file}")
    
    print(f"\nLOCATION: Templates generated in: {OUTPUT_DIR}")
    print(f"\nSTAGE RANGE: {STAGE_RANGE[0]}-{STAGE_RANGE[1]} ({STAGE_CONFIG['description']})")
    print("SEQUENCE PATTERN: Progressive amplification (a->b->c->d)")
    print("   Step a: Comprehensive expansion")
    print("   Step b: Focused amplification")  
    print("   Step c: Essential enhancement")
    print("   Step d: Core value boost")


if __name__ == "__main__":
    main()
