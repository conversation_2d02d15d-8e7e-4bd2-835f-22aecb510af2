#!/usr/bin/env python3

#===================================================================
# IMPORTS
#===================================================================
import os
import re
import json
import glob
import sys
import datetime


#===================================================================
# BASE CONFIG
#===================================================================
class TemplateConfig:
    LEVEL = None
    FORMAT = None
    SOURCE_DIR = None

    # Stage-based ID organization
    STAGES = {
        "stage1": {"range": (1000, 1999), "description": "Prototyping/Testing", "auto_id": True},
        "stage2": {"range": (2000, 2999), "description": "Validated/Unplaced", "auto_id": False},
        "stage3": {"range": (3000, 3999), "description": "Finalized/Production", "auto_id": False},
        "stage4": {"range": (4000, 4999), "description": "Reserved", "auto_id": False},
        "stage5": {"range": (5000, 5999), "description": "Reserved", "auto_id": False},
        "stage6": {"range": (6000, 6999), "description": "Reserved", "auto_id": False},
        "stage7": {"range": (7000, 7999), "description": "Reserved", "auto_id": False},
        "stage8": {"range": (8000, 8999), "description": "Reserved", "auto_id": False},
        "stage9": {"range": (9000, 9999), "description": "Reserved", "auto_id": False}
    }

    # Sequence definition
    SEQUENCE = {
        "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
        "id_group": 1,
        "step_group": 2,
        "name_group": 3,
        "order_function": lambda step: ord(step) - ord('a')
    }

    @classmethod
    def get_stage_for_id(cls, template_id):
        """Determine which stage a template ID belongs to."""
        try:
            id_num = int(template_id.split('-')[0])
            for stage_name, stage_info in cls.STAGES.items():
                start, end = stage_info["range"]
                if start <= id_num <= end:
                    return stage_name, stage_info
            return "legacy", {"range": (0, 999), "description": "Legacy/Unorganized", "auto_id": False}
        except (ValueError, IndexError):
            return "unknown", {"range": (0, 0), "description": "Unknown", "auto_id": False}

    @classmethod
    def get_next_auto_id(cls, stage_name, existing_ids):
        """Generate next available ID for auto-ID stages."""
        if stage_name not in cls.STAGES or not cls.STAGES[stage_name]["auto_id"]:
            return None

        start, end = cls.STAGES[stage_name]["range"]
        used_ids = {int(id.split('-')[0]) for id in existing_ids
                   if id.split('-')[0].isdigit() and start <= int(id.split('-')[0]) <= end}

        for id_num in range(start, end + 1):
            if id_num not in used_ids:
                return id_num
        return None

    # Content extraction patterns
    PATTERNS = {}

    # Path helpers
    @classmethod
    def get_output_filename(cls):
        if not cls.FORMAT or not cls.LEVEL:
            raise NotImplementedError("FORMAT/LEVEL required.")
        return f"{cls.LEVEL}.{cls.FORMAT}.templates.json"

    @classmethod
    def get_full_output_path(cls, script_dir):
        return os.path.join(script_dir, cls.get_output_filename())

    @classmethod
    def get_full_source_path(cls, script_dir):
        if not cls.SOURCE_DIR:
            raise NotImplementedError("SOURCE_DIR required.")
        return os.path.join(script_dir, cls.SOURCE_DIR)


#===================================================================
# FORMAT: MARKDOWN (lvl1)
#===================================================================
class TemplateConfigMD(TemplateConfig):
    LEVEL = "lvl1"
    FORMAT = "md"
    SOURCE_DIR = "stage1/md"  # Updated for stage-first structure

    # Keywords to extract from interpretation text
    KEYWORDS_TO_MATCH = [
        "distill", "inherent", "maximally", "maximum", "clarity",
        "precision", "essence", "coherence", "structure", "elegant",
        "resonance", "amplification", "self", "transformation", "potency",
        "recursive", "adaptive", "meta", "synthesis", "potent",
        "totally", "completely",
        # Add other important semantic markers as needed
    ]

    # Combined pattern for lvl1 markdown templates
    _LVL1_MD_PATTERN = re.compile(
        r"\[(.*?)\]"     # Group 1: Title
        r"\s*"           # Match (but don't capture) whitespace AFTER title
        r"(.*?)"         # Group 2: Capture Interpretation text
        r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
        r"(`\{.*?\}`)"   # Group 3: Transformation
    )

    PATTERNS = {
        "title": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(1).strip() if m else ""
        },
        "interpretation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(2).strip() if m else ""
        },
        "transformation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(3).strip() if m else ""
        }
    }


#===================================================================
# HELPERS
#===================================================================
def _extract_field(content, pattern_cfg):
    try:
        match = pattern_cfg["pattern"].search(content)
        return pattern_cfg["extract"](match)
    except Exception:
        return pattern_cfg.get("default", "")


def extract_keywords(text, keywords):
    """Extract keywords from text and return pipe-delimited string."""
    if not text or not keywords:
        return ""

    matches = []
    text_lower = text.lower()
    for keyword in keywords:
        if keyword.lower() in text_lower:
            matches.append(keyword)

    return "|".join(matches) if matches else ""


def _is_extraction_failed(parts):
    """Check if the extraction failed and we should use fallback."""
    title = parts.get("title", "").strip()
    interpretation = parts.get("interpretation", "").strip()
    transformation = parts.get("transformation", "").strip()

    # Check for generic/placeholder content that indicates failed extraction
    generic_indicators = ["Title", "Interpretation", "Transformation", "Execute as:", "`{Transformation}`"]

    # Failed if any part is empty
    if not title or not interpretation or not transformation:
        return True

    # Failed if any part is just a generic placeholder
    if any(part.strip() in generic_indicators for part in [title, interpretation, transformation]):
        return True

    # Failed if interpretation is too short (likely not the real content)
    if len(interpretation) < 50:
        return True

    return False


def _create_fallback_parts(content, template_id):
    """Create fallback parts when regex extraction fails."""
    # Try to extract title from first line if it has brackets
    lines = content.split('\n')
    first_line = lines[0].strip() if lines else ""

    # Extract title from [Title] pattern if present
    title_match = re.match(r'\[(.*?)\]', first_line)
    if title_match:
        title = title_match.group(1).strip()
        # Use the rest of the content as interpretation
        remaining_content = '\n'.join(lines[1:]).strip() if len(lines) > 1 else content
    else:
        # Use template_id as title and full content as interpretation
        title = template_id.replace('-', ' ').replace('_', ' ').title()
        remaining_content = content

    # Leave transformation empty in fallback mode
    transformation = ""

    return {
        "title": title,
        "interpretation": remaining_content,
        "transformation": transformation
    }


def extract_metadata(content, template_id, config):
    content = content.strip()
    parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}

    # Check if extraction failed and use fallback if needed
    if _is_extraction_failed(parts):
        print(f"INFO: Using fallback extraction for {template_id}")
        parts = _create_fallback_parts(content, template_id)

    # Extract keywords from interpretation text
    if hasattr(config, 'KEYWORDS_TO_MATCH'):
        interpretation = parts.get("interpretation", "")
        if interpretation:
            keywords = extract_keywords(interpretation, config.KEYWORDS_TO_MATCH)
            parts["keywords"] = keywords

    return {"raw": content, "parts": parts}


#===================================================================
# CATALOG GENERATION
#===================================================================
def generate_catalog(config, script_dir):
    # Find templates and extract metadata
    source_path = config.get_full_source_path(script_dir)
    template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

    print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
    print(f"Source: {source_path} (*.{config.FORMAT})")
    print(f"Found {len(template_files)} template files.")

    templates = {}
    sequences = {}

    # Process each template file
    for file_path in template_files:
        filename = os.path.basename(file_path)
        template_id = os.path.splitext(filename)[0]

        try:
            # Read content with proper UTF-8 encoding
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()

            # Extract and store template metadata
            template_data = extract_metadata(content, template_id, config)
            templates[template_id] = template_data

            # Process sequence information from filename
            seq_match = config.SEQUENCE["pattern"].match(template_id)
            if seq_match:
                seq_id = seq_match.group(config.SEQUENCE["id_group"])
                step = seq_match.group(config.SEQUENCE["step_group"])
                seq_order = config.SEQUENCE["order_function"](step)
                sequences.setdefault(seq_id, []).append({
                    "template_id": template_id, "step": step, "order": seq_order
                })
            print(f"SUCCESS: Processed {template_id}")
        except Exception as e:
            print(f"ERROR: {template_id} -> {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()

    # Sort sequence steps
    for seq_id, steps in sequences.items():
        try:
            steps.sort(key=lambda step: step["order"])
        except Exception as e:
            print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

    # Analyze stage distribution
    stage_distribution = {}
    for template_id in templates.keys():
        stage_name, stage_info = config.get_stage_for_id(template_id)
        if stage_name not in stage_distribution:
            stage_distribution[stage_name] = {
                "count": 0,
                "description": stage_info["description"],
                "range": stage_info["range"],
                "auto_id": stage_info["auto_id"],
                "templates": []
            }
        stage_distribution[stage_name]["count"] += 1
        stage_distribution[stage_name]["templates"].append(template_id)

    # Create catalog with metadata including stage analysis
    timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
    return {
        "catalog_meta": {
            "level": config.LEVEL,
            "format": config.FORMAT,
            "generated_at": timestamp,
            "source_directory": config.SOURCE_DIR,
            "total_templates": len(templates),
            "total_sequences": len(sequences),
            "stage_distribution": stage_distribution
        },
        "templates": templates,
        "sequences": sequences
    }


def save_catalog(catalog_data, config, script_dir):
    output_path = config.get_full_output_path(script_dir)
    print(f"Output: {output_path}")

    try:
        with open(output_path, 'w', encoding='utf-8', errors='replace') as f:
            json.dump(catalog_data, f, indent=2, ensure_ascii=False)
            print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
            print("--- Catalog Generation Complete ---")
        return output_path
    except Exception as e:
        print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
        return None


#===================================================================
# STAGE MANAGEMENT UTILITIES
#===================================================================
def print_stage_distribution(catalog):
    """Print stage distribution analysis."""
    if "stage_distribution" not in catalog.get("catalog_meta", {}):
        print("No stage distribution data available.")
        return

    print("\n=== Stage Distribution Analysis ===")
    stage_dist = catalog["catalog_meta"]["stage_distribution"]

    for stage_name in sorted(stage_dist.keys()):
        stage_info = stage_dist[stage_name]
        print(f"\n{stage_name.upper()}: {stage_info['description']}")
        print(f"  Range: {stage_info['range'][0]}-{stage_info['range'][1]}")
        print(f"  Auto-ID: {'Yes' if stage_info['auto_id'] else 'No'}")
        print(f"  Templates: {stage_info['count']}")

        if stage_info['templates']:
            # Group by category for better display
            templates_by_category = {}
            for template_id in sorted(stage_info['templates']):
                category = template_id.split('-')[-1].split('_')[0] if '_' in template_id else 'misc'
                if category not in templates_by_category:
                    templates_by_category[category] = []
                templates_by_category[category].append(template_id)

            for category, template_ids in sorted(templates_by_category.items()):
                print(f"    {category}: {', '.join(template_ids)}")

def get_next_stage1_id(catalog):
    """Get the next available auto-generated ID for stage1."""
    existing_ids = list(catalog.get("templates", {}).keys())
    return TemplateConfig.get_next_auto_id("stage1", existing_ids)

def validate_stage_compliance(catalog):
    """Validate that templates are in appropriate stages."""
    issues = []
    templates = catalog.get("templates", {})

    for template_id, template_data in templates.items():
        stage_name, _ = TemplateConfig.get_stage_for_id(template_id)

        # Check if template should be moved to a different stage
        if stage_name == "legacy" and template_id.startswith(('0', '5', '6', '7', '8', '9')):
            issues.append(f"Template {template_id} is in legacy range but could be reorganized")

        # Check for stage3 templates that might need validation
        if stage_name == "stage3":
            # Stage3 should be finalized - check for completeness
            parts = template_data.get("parts", {})
            if not all(key in parts for key in ["title", "interpretation", "transformation"]):
                issues.append(f"Stage3 template {template_id} missing required parts")

    return issues


def show_stage_overview(catalog):
    """Display comprehensive stage overview."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass

    print("=" * 60)
    print("STAGE-BASED TEMPLATE ORGANIZATION SYSTEM")
    print("=" * 60)

    print("\nSTAGE DEFINITIONS:")
    for stage_name, stage_info in TemplateConfig.STAGES.items():
        start, end = stage_info["range"]
        auto_id = "YES" if stage_info["auto_id"] else "NO"
        print(f"  {stage_name.upper()}: {start:4d}-{end:4d} | {stage_info['description']} | Auto-ID: {auto_id}")

    print_stage_distribution(catalog)


def show_migration_suggestions(catalog):
    """Suggest templates that could be migrated between stages."""
    print("\nMIGRATION SUGGESTIONS:")

    templates = catalog.get("templates", {})
    suggestions = []

    # Find legacy templates that could be moved to proper stages
    for template_id in templates.keys():
        stage_name, _ = TemplateConfig.get_stage_for_id(template_id)

        if stage_name == "legacy":
            if template_id.startswith(('0', '5', '6', '7', '8', '9')):
                suggestions.append(f"   ARCHIVE: {template_id} -> Consider moving to appropriate stage")

    # Find stage1 templates that might be ready for stage2/3
    stage1_templates = [tid for tid in templates.keys()
                       if TemplateConfig.get_stage_for_id(tid)[0] == "stage1"]

    if len(stage1_templates) > 5:  # Arbitrary threshold
        suggestions.append(f"   REVIEW: {len(stage1_templates)} Stage1 templates -> Review for promotion to Stage2/3")

    if suggestions:
        for suggestion in suggestions:
            print(suggestion)
    else:
        print("   SUCCESS: No migration suggestions at this time")


def demonstrate_workflow():
    """Demonstrate the complete stage-based workflow."""
    print("\n" + "=" * 60)
    print("STAGE-BASED WORKFLOW DEMONSTRATION")
    print("=" * 60)

    print("\n1️⃣  PROTOTYPING WORKFLOW (Stage 1):")
    print("   • Get next auto-ID for new template")
    print("   • Create template with auto-generated ID")
    print("   • Test and iterate in Stage 1 range")
    print("   • No manual ID management required")

    print("\n2️⃣  VALIDATION WORKFLOW (Stage 2):")
    print("   • Move proven templates from Stage 1")
    print("   • Templates are validated but not yet categorized")
    print("   • Manual ID assignment for organization")
    print("   • Prepare for final production placement")

    print("\n3️⃣  PRODUCTION WORKFLOW (Stage 3):")
    print("   • Finalized, production-ready templates")
    print("   • Stable IDs that won't change")
    print("   • Full compliance with RulesForAI.md")
    print("   • Ready for system integration")

    print("\n4️⃣  EXPANSION WORKFLOW (Stage 4-9):")
    print("   • Reserved for future system expansion")
    print("   • Domain-specific template collections")
    print("   • Specialized template categories")
    print("   • Advanced template sequences")

#===================================================================
# IMPORTABLE API FOR EXTERNAL SCRIPTS
#===================================================================
def get_default_catalog_path(script_dir=None):
    """Get the path to the default catalog file."""
    if script_dir is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
    return TemplateConfigMD().get_full_output_path(script_dir)


def load_catalog(catalog_path=None):
    """Load a catalog from a JSON file."""
    if catalog_path is None:
        catalog_path = get_default_catalog_path()

    if not os.path.exists(catalog_path):
        raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")


def get_template(catalog, template_id):
    """Get a template by its ID."""
    if "templates" not in catalog or template_id not in catalog["templates"]:
        return None
    return catalog["templates"][template_id]


def get_sequence(catalog, sequence_id):
    """Get all templates in a sequence, ordered by steps."""
    if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
        return []

    sequence_steps = catalog["sequences"][sequence_id]
    return [(step["step"], get_template(catalog, step["template_id"]))
            for step in sequence_steps]


def get_all_sequences(catalog):
    """Get a list of all sequence IDs."""
    if "sequences" not in catalog:
        return []
    return list(catalog["sequences"].keys())


def get_system_instruction(template):
    """Convert a template to a system instruction format."""
    if not template or "parts" not in template:
        return None

    parts = template["parts"]
    instruction = f"# {parts.get('title', '')}\n\n"

    if "interpretation" in parts and parts["interpretation"]:
        instruction += f"{parts['interpretation']}\n\n"

    if "transformation" in parts and parts["transformation"]:
        instruction += parts["transformation"]

    return instruction


def regenerate_catalog(force=False):
    """Regenerate the catalog file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config = TemplateConfigMD()
    catalog_path = config.get_full_output_path(script_dir)

    if os.path.exists(catalog_path) and not force:
        # Check if catalog is older than any template file
        catalog_mtime = os.path.getmtime(catalog_path)
        templates_dir = config.get_full_source_path(script_dir)

        needs_update = False
        for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
            if os.path.getmtime(file_path) > catalog_mtime:
                needs_update = True
                break

        if not needs_update:
            # print("Catalog is up to date")
            return load_catalog(catalog_path)

    # Generate and save new catalog
    catalog = generate_catalog(config, script_dir)
    save_catalog(catalog, config, script_dir)
    return catalog


#===================================================================
# SCRIPT EXECUTION
#===================================================================
if __name__ == "__main__":
    import argparse

    SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

    parser = argparse.ArgumentParser(description="Generate template catalog with stage-based organization")
    parser.add_argument("--force", action="store_true", help="Force regeneration even if files are up to date")
    parser.add_argument("--show-stages", action="store_true", help="Show stage distribution analysis and exit")
    parser.add_argument("--next-stage1-id", action="store_true", help="Show next available stage1 auto-ID and exit")
    parser.add_argument("--validate-stages", action="store_true", help="Validate stage compliance and exit")
    parser.add_argument("--stage-overview", action="store_true", help="Show complete stage system overview")
    parser.add_argument("--migration-suggestions", action="store_true", help="Show template migration suggestions")
    parser.add_argument("--workflow-demo", action="store_true", help="Demonstrate stage-based workflow")
    parser.add_argument("--api-test", action="store_true", help="Run API test and list sequences")
    args = parser.parse_args()

    try:
        config_to_use = TemplateConfigMD
        active_config = config_to_use()

        # Load existing catalog for analysis commands
        analysis_commands = [args.show_stages, args.next_stage1_id, args.validate_stages,
                           args.stage_overview, args.migration_suggestions, args.workflow_demo, args.api_test]
        if any(analysis_commands):
            catalog = regenerate_catalog(force=False)

            if args.show_stages:
                print_stage_distribution(catalog)
                sys.exit(0)

            if args.next_stage1_id:
                next_id = get_next_stage1_id(catalog)
                if next_id:
                    print(f"NEXT PROTOTYPE ID: {next_id}")
                    print(f"   Suggested format: {next_id}-a-your_template_name.md")
                    print(f"   Category examples:")
                    print(f"     {next_id}-a-content_analyzer.md")
                    print(f"     {next_id}-a-text_transformer.md")
                else:
                    print("WARNING: No available IDs in Stage1 range (1000-1999)")
                sys.exit(0)

            if args.validate_stages:
                print("STAGE COMPLIANCE VALIDATION:")
                issues = validate_stage_compliance(catalog)
                if issues:
                    print(f"   WARNING: Found {len(issues)} compliance issues:")
                    for issue in issues:
                        print(f"      - {issue}")
                else:
                    print("   SUCCESS: All templates are stage-compliant")
                sys.exit(0)

            if args.stage_overview:
                show_stage_overview(catalog)
                sys.exit(0)

            if args.migration_suggestions:
                show_migration_suggestions(catalog)
                sys.exit(0)

            if args.workflow_demo:
                demonstrate_workflow()
                sys.exit(0)

            if args.api_test:
                print("\nAvailable sequences:")
                for seq_id in get_all_sequences(catalog):
                    sequence_steps = get_sequence(catalog, seq_id)
                    if sequence_steps and sequence_steps[0][1] and "parts" in sequence_steps[0][1]:
                        first_step_title = sequence_steps[0][1]["parts"]["title"]
                        print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")
                sys.exit(0)

        # Regular catalog generation
        catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
        save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        # Show stage distribution after generation
        print_stage_distribution(catalog)

    except NotImplementedError as e:
        print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
        sys.exit(1)
    except FileNotFoundError as e:
        print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"FATAL ERROR: {e}", file=sys.stderr)
        sys.exit(1)
