"""
RunwayML Gen-4 Image Prompt Generator
Schema-driven protocol for converting any input into optimized RunwayML-compatible image prompts
Based on community workflow analysis and best practices
"""

import re
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class ReferenceType(Enum):
    CHARACTER = "character"
    POSE = "pose" 
    LOCATION = "location"
    STYLE = "style"
    OBJECT = "object"
    COMPOSITION = "composition"
    LIGHTING = "lighting"

class VisualStyle(Enum):
    PHOTOREALISTIC = "photorealistic"
    CINEMATIC = "cinematic"
    ARTISTIC = "artistic"
    GAME_ASSET = "game_asset"
    ARCHITECTURAL = "architectural"
    SURREAL = "surreal"
    MINIMALIST = "minimalist"

@dataclass
class PromptComponents:
    primary_subject: str
    spatial_positioning: str
    reference_strategy: str
    lighting_mood: str
    style_specification: str
    compositional_elements: List[str]
    visual_attributes: List[str]

class ComprehensiveRunwayImageGenerator:
    """
    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis
    Implements community-validated workflows and reference-driven composition
    """
    
    def __init__(self):
        self.max_prompt_length = 500
        self.community_patterns = self._load_community_patterns()
        self.reference_workflows = self._load_reference_workflows()
    
    def _load_community_patterns(self) -> Dict[str, str]:
        """Load validated community prompt patterns"""
        return {
            "character_consistency": "IMG_{n} as {character_description} in {setting}",
            "spatial_positioning": "place {subject} at {position} using {reference_layout}",
            "multi_reference": "combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}",
            "chess_grid": "position {object} at {coordinate} in {scene_layout}",
            "pose_control": "give {character} the pose from IMG_{n} in {environment}",
            "style_transfer": "apply the style of IMG_{n} to {subject} while preserving {elements}",
            "lighting_direction": "illuminate {subject} with lighting direction from IMG_{n}",
            "object_extraction": "extract {object} from IMG_{n} and place in {new_context}",
            "scene_blocking": "block scene using IMG_{n} composition with {modifications}",
            "weighting_control": "blend {percentage1}% {element1} with {percentage2}% {element2}"
        }
    
    def _load_reference_workflows(self) -> Dict[str, Dict]:
        """Load community-validated reference workflows"""
        return {
            "character_generation": {
                "pattern": "{character} as {archetype} in {setting}",
                "references": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],
                "examples": ["mysterious NPC villager", "professional NASA astronaut"]
            },
            "scene_composition": {
                "pattern": "render scene with IMG_1 {composition} using IMG_2 {elements}",
                "references": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],
                "spatial_control": True
            },
            "object_placement": {
                "pattern": "place {object} at {position} maintaining {reference_aspect}",
                "references": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],
                "precision_positioning": True
            },
            "style_application": {
                "pattern": "apply {style_reference} to {subject} preserving {core_elements}",
                "references": [ReferenceType.STYLE, ReferenceType.CHARACTER],
                "style_transfer": True
            }
        }
    
    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:
        """
        Main execution function implementing the schema-driven protocol
        """
        # Step 3a: Analyze visual core and compositional intent
        visual_analysis = self._analyze_visual_essence(source_concept)
        
        # Step 3b: Identify primary subjects and critical elements
        subject_analysis = self._identify_primary_subjects(visual_analysis)
        
        # Step 3c: Integrate advanced image-generation attributes
        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)
        
        # Step 3d: Emphasize high-impact visual parameters
        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)
        
        # Step 3e: Structure and refine for RunwayML compatibility
        structured_prompt = self._structure_runway_prompt(visual_parameters)
        
        # Step 3f: Validate compliance and character count
        validated_prompt = self._validate_runway_compliance(structured_prompt)
        
        return {
            "runwayml_image_prompt": validated_prompt["prompt"],
            "reference_strategy": validated_prompt["strategy"],
            "workflow_type": validated_prompt["workflow"],
            "reference_requirements": validated_prompt["references"]
        }
    
    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:
        """Extract visual core, semantic narrative, and compositional intent"""
        concept_str = str(source_concept).lower()
        
        # Identify core visual elements
        visual_elements = self._extract_visual_elements(concept_str)
        
        # Determine semantic narrative
        narrative_context = self._determine_narrative_context(concept_str)
        
        # Assess compositional requirements
        composition_needs = self._assess_composition_needs(concept_str)
        
        return {
            "visual_elements": visual_elements,
            "narrative_context": narrative_context,
            "composition_needs": composition_needs,
            "complexity_level": self._assess_complexity(concept_str)
        }
    
    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:
        """Extract key visual components using pattern matching"""
        elements = {
            "subjects": [],
            "objects": [],
            "environments": [],
            "actions": [],
            "qualities": []
        }
        
        # Subject detection patterns
        subject_patterns = [
            r'\b(person|character|figure|individual|being)\b',
            r'\b(man|woman|child|adult|human)\b',
            r'\b(creature|animal|monster|entity)\b'
        ]
        
        # Object detection patterns  
        object_patterns = [
            r'\b(building|structure|architecture)\b',
            r'\b(vehicle|car|ship|aircraft)\b',
            r'\b(furniture|equipment|tool|device)\b'
        ]
        
        # Environment patterns
        environment_patterns = [
            r'\b(landscape|cityscape|interior|exterior)\b',
            r'\b(forest|ocean|mountain|desert|urban)\b',
            r'\b(room|hall|street|park|field)\b'
        ]
        
        # Extract using patterns
        for pattern in subject_patterns:
            elements["subjects"].extend(re.findall(pattern, concept))
        
        for pattern in object_patterns:
            elements["objects"].extend(re.findall(pattern, concept))
            
        for pattern in environment_patterns:
            elements["environments"].extend(re.findall(pattern, concept))
        
        return elements

    def _determine_narrative_context(self, concept: str) -> str:
        """Determine the semantic narrative and context"""
        context_indicators = {
            "cinematic": ["film", "movie", "cinematic", "dramatic", "scene"],
            "architectural": ["building", "structure", "interior", "design", "space"],
            "character": ["person", "character", "portrait", "figure", "individual"],
            "product": ["product", "object", "item", "design", "concept"],
            "environment": ["landscape", "environment", "setting", "location", "place"],
            "artistic": ["art", "artistic", "creative", "abstract", "stylized"],
            "game": ["game", "gaming", "asset", "ui", "interface", "hud"]
        }

        for context, keywords in context_indicators.items():
            if any(keyword in concept for keyword in keywords):
                return context

        return "general"

    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:
        """Assess what compositional elements are needed"""
        return {
            "spatial_positioning": any(word in concept for word in ["position", "place", "location", "where", "at"]),
            "character_consistency": any(word in concept for word in ["character", "person", "same", "consistent"]),
            "multi_reference": any(word in concept for word in ["combine", "merge", "blend", "mix", "multiple"]),
            "style_transfer": any(word in concept for word in ["style", "look", "aesthetic", "mood", "feel"]),
            "lighting_control": any(word in concept for word in ["light", "lighting", "illuminate", "shadow", "bright"]),
            "object_extraction": any(word in concept for word in ["extract", "remove", "isolate", "separate"])
        }

    def _assess_complexity(self, concept: str) -> str:
        """Assess the complexity level of the request"""
        word_count = len(concept.split())
        reference_indicators = concept.count("image") + concept.count("reference") + concept.count("img")

        if word_count > 50 or reference_indicators > 2:
            return "complex"
        elif word_count > 20 or reference_indicators > 0:
            return "moderate"
        else:
            return "simple"

    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:
        """Identify and amplify primary subjects and critical visual elements"""
        elements = visual_analysis["visual_elements"]
        context = visual_analysis["narrative_context"]

        # Prioritize subjects based on context
        primary_subjects = self._prioritize_subjects(elements["subjects"], context)

        # Identify critical visual elements
        critical_elements = self._identify_critical_elements(elements, context)

        # Determine key actions or states
        key_actions = self._extract_key_actions(elements.get("actions", []))

        return {
            "primary_subjects": primary_subjects,
            "critical_elements": critical_elements,
            "key_actions": key_actions,
            "reference_requirements": self._determine_reference_requirements(visual_analysis)
        }

    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:
        """Prioritize subjects based on context and importance"""
        priority_map = {
            "character": ["person", "character", "figure", "individual"],
            "cinematic": ["character", "person", "figure"],
            "architectural": ["building", "structure"],
            "product": ["object", "item", "product"]
        }

        if context in priority_map:
            prioritized = []
            for priority_subject in priority_map[context]:
                if priority_subject in subjects:
                    prioritized.append(priority_subject)

            # Add remaining subjects
            for subject in subjects:
                if subject not in prioritized:
                    prioritized.append(subject)

            return prioritized

        return subjects

    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:
        """Identify critical visual elements based on context"""
        critical = []

        # Context-specific critical elements
        if context == "cinematic":
            critical.extend(["lighting", "composition", "mood", "atmosphere"])
        elif context == "architectural":
            critical.extend(["structure", "materials", "lighting", "perspective"])
        elif context == "character":
            critical.extend(["pose", "expression", "clothing", "setting"])
        elif context == "product":
            critical.extend(["form", "materials", "lighting", "background"])

        # Add elements from input
        critical.extend(elements.get("objects", []))
        critical.extend(elements.get("environments", []))

        return list(set(critical))  # Remove duplicates

    def _extract_key_actions(self, actions: List[str]) -> List[str]:
        """Extract and prioritize key actions or states"""
        # Filter out motion-related actions (per constraints)
        static_actions = []
        motion_keywords = ["moving", "running", "flying", "walking", "motion", "movement"]

        for action in actions:
            if not any(motion_word in action.lower() for motion_word in motion_keywords):
                static_actions.append(action)

        return static_actions

    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:
        """Determine what types of references are needed"""
        requirements = []
        composition_needs = visual_analysis["composition_needs"]
        context = visual_analysis["narrative_context"]

        # Map composition needs to reference types
        if composition_needs["character_consistency"]:
            requirements.append(ReferenceType.CHARACTER)

        if composition_needs["spatial_positioning"]:
            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])

        if composition_needs["style_transfer"]:
            requirements.append(ReferenceType.STYLE)

        if composition_needs["lighting_control"]:
            requirements.append(ReferenceType.LIGHTING)

        if composition_needs["object_extraction"]:
            requirements.append(ReferenceType.OBJECT)

        # Context-specific requirements
        if context == "architectural":
            requirements.append(ReferenceType.LOCATION)
        elif context == "character":
            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])

        return list(set(requirements))  # Remove duplicates

    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:
        """Integrate advanced image-generation attributes"""
        primary_subject = ", ".join(subject_analysis["primary_subjects"][:2])  # Limit to top 2

        # Determine spatial positioning strategy
        spatial_positioning = self._generate_spatial_positioning(subject_analysis)

        # Select reference strategy based on requirements
        reference_strategy = self._select_reference_strategy(subject_analysis["reference_requirements"])

        # Generate lighting and mood specifications
        lighting_mood = self._generate_lighting_mood(subject_analysis)

        # Determine style specification
        style_specification = self._determine_style_specification(subject_analysis)

        # Compile compositional elements
        compositional_elements = subject_analysis["critical_elements"][:4]  # Limit to top 4

        # Generate visual attributes
        visual_attributes = self._generate_visual_attributes(subject_analysis)

        return PromptComponents(
            primary_subject=primary_subject,
            spatial_positioning=spatial_positioning,
            reference_strategy=reference_strategy,
            lighting_mood=lighting_mood,
            style_specification=style_specification,
            compositional_elements=compositional_elements,
            visual_attributes=visual_attributes
        )

    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:
        """Generate spatial positioning instructions"""
        if ReferenceType.COMPOSITION in subject_analysis["reference_requirements"]:
            return "using IMG_1 composition and spatial layout"
        elif ReferenceType.POSE in subject_analysis["reference_requirements"]:
            return "positioned according to IMG_1 pose reference"
        else:
            return "centered composition with balanced framing"

    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:
        """Select optimal reference strategy based on requirements"""
        if len(requirements) >= 3:
            return "multi-reference workflow combining character, pose, and environment"
        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:
            return "character consistency with pose control"
        elif ReferenceType.STYLE in requirements:
            return "style transfer maintaining core elements"
        elif ReferenceType.COMPOSITION in requirements:
            return "compositional reference for spatial control"
        else:
            return "single reference for primary element control"

    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:
        """Generate lighting and mood specifications"""
        if ReferenceType.LIGHTING in subject_analysis["reference_requirements"]:
            return "lighting direction and mood from reference image"
        else:
            # Default lighting based on context
            critical_elements = subject_analysis["critical_elements"]
            if "cinematic" in str(critical_elements):
                return "cinematic lighting with dramatic shadows"
            elif "architectural" in str(critical_elements):
                return "natural lighting with architectural detail emphasis"
            else:
                return "balanced lighting with clear detail visibility"

    def _determine_style_specification(self, subject_analysis: Dict) -> str:
        """Determine style specification"""
        if ReferenceType.STYLE in subject_analysis["reference_requirements"]:
            return "style matching reference aesthetic"
        else:
            # Infer style from critical elements
            elements = subject_analysis["critical_elements"]
            if any("architectural" in str(elem) for elem in elements):
                return "photorealistic architectural rendering"
            elif any("character" in str(elem) for elem in elements):
                return "high-detail character portrait"
            else:
                return "photorealistic with enhanced detail"

    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:
        """Generate high-impact visual attributes"""
        attributes = []

        # Base quality attributes
        attributes.extend(["high detail", "sharp focus", "professional quality"])

        # Context-specific attributes
        critical_elements = subject_analysis["critical_elements"]
        if "lighting" in critical_elements:
            attributes.append("dramatic lighting")
        if "composition" in critical_elements:
            attributes.append("balanced composition")
        if "mood" in critical_elements:
            attributes.append("atmospheric mood")

        # Reference-specific attributes
        requirements = subject_analysis["reference_requirements"]
        if ReferenceType.CHARACTER in requirements:
            attributes.append("character consistency")
        if ReferenceType.POSE in requirements:
            attributes.append("precise pose control")

        return attributes[:6]  # Limit to top 6 attributes

    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:
        """Emphasize high-impact visual parameters while excluding motion"""
        emphasized = {
            "photorealism": "photorealistic rendering with enhanced detail",
            "stylization": f"{components.style_specification} with artistic enhancement",
            "composition": f"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}",
            "lighting": f"{components.lighting_mood} creating visual depth",
            "detail": f"high-resolution detail in {components.primary_subject}",
            "atmosphere": f"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}"
        }

        # Filter out any motion-related terms
        motion_terms = ["movement", "motion", "camera", "fps", "video", "animation"]
        for key, value in emphasized.items():
            for term in motion_terms:
                if term in value.lower():
                    emphasized[key] = value.replace(term, "static").replace("  ", " ")

        return emphasized

    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:
        """Structure and refine prompt for RunwayML compatibility"""
        # Build core prompt structure
        core_elements = [
            visual_parameters["detail"],
            visual_parameters["composition"],
            visual_parameters["lighting"],
            visual_parameters["photorealism"]
        ]

        # Combine into coherent prompt
        structured_prompt = ", ".join(core_elements)

        # Add reference strategy
        reference_strategy = self._format_reference_strategy(visual_parameters)

        # Determine workflow type
        workflow_type = self._determine_workflow_type(visual_parameters)

        return {
            "prompt": structured_prompt,
            "reference_strategy": reference_strategy,
            "workflow_type": workflow_type
        }

    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:
        """Format reference strategy for implementation"""
        if "multi-reference" in visual_parameters.get("composition", ""):
            return "Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood"
        elif "character consistency" in visual_parameters.get("detail", ""):
            return "Use IMG_1 for character reference, maintain consistency across generation"
        elif "pose control" in visual_parameters.get("composition", ""):
            return "Use IMG_1 for pose reference, apply to subject in new context"
        else:
            return "Use single reference image for primary visual control"

    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:
        """Determine the appropriate community workflow type"""
        composition = visual_parameters.get("composition", "").lower()
        detail = visual_parameters.get("detail", "").lower()

        if "character" in detail and "pose" in composition:
            return "character_generation"
        elif "composition" in composition and "spatial" in composition:
            return "scene_composition"
        elif "lighting" in visual_parameters.get("lighting", "").lower():
            return "lighting_control"
        else:
            return "general_enhancement"

    def _validate_runway_compliance(self, structured_prompt: Dict[str, str]) -> Dict[str, str]:
        """Validate compliance with RunwayML formatting and character limits"""
        prompt = structured_prompt["prompt"]

        # Ensure character limit compliance
        if len(prompt) > self.max_prompt_length:
            prompt = self._truncate_prompt_intelligently(prompt)

        # Validate RunwayML syntax
        validated_prompt = self._ensure_runway_syntax(prompt)

        # Ensure no motion terminology
        validated_prompt = self._remove_motion_terms(validated_prompt)

        # Format reference requirements
        reference_requirements = self._format_reference_requirements(structured_prompt)

        return {
            "prompt": validated_prompt,
            "strategy": structured_prompt["reference_strategy"],
            "workflow": structured_prompt["workflow_type"],
            "references": reference_requirements,
            "character_count": len(validated_prompt)
        }

    def _truncate_prompt_intelligently(self, prompt: str) -> str:
        """Intelligently truncate prompt while preserving key elements"""
        # Split into components
        components = [comp.strip() for comp in prompt.split(",")]

        # Prioritize components (keep first 4 most important)
        priority_components = components[:4]

        # Rebuild prompt
        truncated = ", ".join(priority_components)

        # If still too long, remove adjectives
        if len(truncated) > self.max_prompt_length:
            truncated = self._remove_excessive_adjectives(truncated)

        return truncated

    def _remove_excessive_adjectives(self, prompt: str) -> str:
        """Remove excessive adjectives to reduce length"""
        # Common adjectives that can be removed
        removable_adjectives = [
            "enhanced", "professional", "high-quality", "detailed",
            "beautiful", "stunning", "amazing", "incredible"
        ]

        for adj in removable_adjectives:
            prompt = prompt.replace(f"{adj} ", "")
            prompt = prompt.replace(f" {adj}", "")

        return prompt.strip()

    def _ensure_runway_syntax(self, prompt: str) -> str:
        """Ensure prompt follows RunwayML syntax conventions"""
        # Remove double spaces
        prompt = re.sub(r'\s+', ' ', prompt)

        # Ensure proper comma separation
        prompt = re.sub(r'\s*,\s*', ', ', prompt)

        # Remove trailing punctuation except periods
        prompt = re.sub(r'[,;:]+$', '', prompt)

        # Ensure single sentence structure
        if not prompt.endswith('.'):
            prompt += '.'

        return prompt.strip()

    def _remove_motion_terms(self, prompt: str) -> str:
        """Remove any motion-related terminology"""
        motion_terms = [
            "moving", "motion", "movement", "camera movement", "fps",
            "video", "animation", "flying", "running", "walking",
            "panning", "zooming", "tracking", "dolly", "crane"
        ]

        for term in motion_terms:
            prompt = prompt.replace(term, "")
            prompt = prompt.replace(term.capitalize(), "")

        # Clean up any double spaces created
        prompt = re.sub(r'\s+', ' ', prompt)

        return prompt.strip()

    def _format_reference_requirements(self, structured_prompt: Dict[str, str]) -> str:
        """Format reference requirements for user guidance"""
        workflow = structured_prompt["workflow_type"]
        strategy = structured_prompt["reference_strategy"]

        if "multi-reference" in strategy:
            return "Requires 2-3 reference images: primary composition, character/object, lighting/mood"
        elif "character" in workflow:
            return "Requires 1-2 reference images: character consistency and optional pose control"
        elif "scene" in workflow:
            return "Requires 1-2 reference images: spatial composition and environmental context"
        else:
            return "Requires 1 reference image for primary visual control"

    # Additional helper methods for convenience functions
    def _optimize_for_references(self, prompt: str) -> str:
        """Optimize prompt for reference efficiency"""
        # Prioritize reference-driven language
        reference_optimized = prompt.replace("high detail", "reference-guided detail")
        reference_optimized = reference_optimized.replace("composition", "reference composition")
        return reference_optimized

    def _extract_core_visual_elements(self, concept: str) -> Dict[str, str]:
        """Extract only the most essential visual elements"""
        concept_lower = concept.lower()

        # Extract primary subject
        subject_match = re.search(r'\b(character|person|building|object|creature|figure)\b', concept_lower)
        primary_subject = subject_match.group(1) if subject_match else "subject"

        # Extract primary action/state
        action_match = re.search(r'\b(standing|sitting|positioned|placed|located)\b', concept_lower)
        primary_action = action_match.group(1) if action_match else "positioned"

        # Extract environment
        env_match = re.search(r'\b(interior|exterior|landscape|room|space|setting)\b', concept_lower)
        environment = env_match.group(1) if env_match else "environment"

        return {
            "subject": primary_subject,
            "action": primary_action,
            "environment": environment
        }

    def _determine_primary_reference_type(self, core_elements: Dict[str, str]) -> ReferenceType:
        """Determine the primary reference type needed"""
        subject = core_elements["subject"]

        if subject in ["character", "person", "figure"]:
            return ReferenceType.CHARACTER
        elif subject in ["building", "space", "room"]:
            return ReferenceType.LOCATION
        elif subject == "object":
            return ReferenceType.OBJECT
        else:
            return ReferenceType.COMPOSITION

    def _build_precision_prompt(self, core_elements: Dict[str, str], reference_type: ReferenceType) -> str:
        """Build precision prompt with maximum reference control"""
        subject = core_elements["subject"]
        action = core_elements["action"]
        environment = core_elements["environment"]

        if reference_type == ReferenceType.CHARACTER:
            return f"{subject} from IMG_1 {action} in {environment}, maintaining character consistency"
        elif reference_type == ReferenceType.LOCATION:
            return f"{subject} using IMG_1 spatial layout and environmental context"
        elif reference_type == ReferenceType.OBJECT:
            return f"{subject} from IMG_1 placed in {environment} with precise positioning"
        else:
            return f"{subject} {action} using IMG_1 compositional reference"

    def _distill_reference_essence(self, input_str: str) -> str:
        """Distill input to absolute reference essence"""
        # Extract only the most critical element
        words = input_str.lower().split()

        # Priority keywords for essence
        essence_keywords = ["character", "building", "object", "scene", "portrait", "landscape"]

        for keyword in essence_keywords:
            if keyword in words:
                return keyword

        # Fallback to first noun
        nouns = ["person", "place", "thing", "concept", "design", "image"]
        for noun in nouns:
            if noun in words:
                return noun

        return "subject"

    def _optimize_spatial_composition(self, essence: str) -> str:
        """Optimize spatial composition for essence"""
        composition_map = {
            "character": "centered portrait composition",
            "building": "architectural perspective composition",
            "object": "product placement composition",
            "scene": "environmental composition",
            "portrait": "portrait composition",
            "landscape": "landscape composition"
        }

        return composition_map.get(essence, "balanced composition")

    def _build_core_prompt(self, essence: str, composition: str) -> str:
        """Build core prompt with maximum efficiency"""
        return f"{essence} with {composition} using reference control"


# Convenience functions for different complexity levels
def generate_comprehensive_prompt(source_concept: Any) -> Dict[str, str]:
    """Generate comprehensive RunwayML prompt with full analysis"""
    generator = ComprehensiveRunwayImageGenerator()
    return generator.generate_runway_prompt(source_concept)

def generate_focused_prompt(image_concept: str) -> Dict[str, str]:
    """Generate focused prompt emphasizing reference integration"""
    generator = ComprehensiveRunwayImageGenerator()

    # Simplified processing for focused approach
    result = generator.generate_runway_prompt(image_concept)

    # Optimize for reference efficiency
    optimized_prompt = generator._optimize_for_references(result["runwayml_image_prompt"])

    return {
        "optimized_prompt": optimized_prompt,
        "reference_plan": result["reference_strategy"],
        "workflow_type": result["workflow_type"]
    }

def generate_precision_prompt(concept: str) -> Dict[str, str]:
    """Generate precision prompt with maximum reference control"""
    generator = ComprehensiveRunwayImageGenerator()

    # Extract core elements only
    core_elements = generator._extract_core_visual_elements(concept)
    reference_type = generator._determine_primary_reference_type(core_elements)

    # Build precise prompt
    precise_prompt = generator._build_precision_prompt(core_elements, reference_type)

    return {
        "precise_prompt": precise_prompt,
        "reference_type": reference_type.value if reference_type else "general"
    }

def generate_core_prompt(input_any: Any) -> str:
    """Generate core essence prompt with maximum efficiency"""
    generator = ComprehensiveRunwayImageGenerator()

    # Distill to absolute essence
    essence = generator._distill_reference_essence(str(input_any))
    composition = generator._optimize_spatial_composition(essence)

    return generator._build_core_prompt(essence, composition)


# Schema-driven protocol implementation functions
def runway_prompt_protocol_0006a(source_concept: Any) -> Dict[str, str]:
    """
    Implementation of 0006-a protocol: comprehensive transformation with reference integration
    """
    generator = ComprehensiveRunwayImageGenerator()
    result = generator.generate_runway_prompt(source_concept)

    return {
        "runway_prompt": result["runwayml_image_prompt"],
        "reference_strategy": result["reference_strategy"]
    }

def runway_prompt_protocol_0006b(image_concept: str) -> Dict[str, str]:
    """
    Implementation of 0006-b protocol: focused optimization with reference efficiency
    """
    return generate_focused_prompt(image_concept)

def runway_prompt_protocol_0006c(concept: str) -> Dict[str, str]:
    """
    Implementation of 0006-c protocol: precision synthesis with maximum reference control
    """
    return generate_precision_prompt(concept)

def runway_prompt_protocol_0006d(input_any: Any) -> str:
    """
    Implementation of 0006-d protocol: core essence with maximum efficiency
    """
    return generate_core_prompt(input_any)


# Example usage and testing
if __name__ == "__main__":
    # Test the comprehensive generator with community workflow patterns
    test_concepts = [
        "A mysterious character in a medieval fantasy setting",
        "Modern architectural interior with dramatic lighting",
        "Product design concept for a futuristic device",
        "Character portrait with specific pose and environment",
        "Combine multiple characters in a single scene",
        "Extract object from one image and place in new environment",
        "Apply artistic style to photorealistic subject",
        "Create game asset with UI elements and HUD"
    ]

    print("RunwayML Gen-4 Image Prompt Generator - Community Workflow Integration")
    print("=" * 80)

    generator = ComprehensiveRunwayImageGenerator()

    for i, concept in enumerate(test_concepts, 1):
        print(f"\n{i}. Input: {concept}")
        print("-" * 60)

        # Test comprehensive protocol
        result = generator.generate_runway_prompt(concept)
        print(f"Prompt: {result['runwayml_image_prompt']}")
        print(f"Strategy: {result['reference_strategy']}")
        print(f"Workflow: {result['workflow_type']}")
        print(f"References: {result['reference_requirements']}")
        print(f"Length: {len(result['runwayml_image_prompt'])} characters")

        # Test focused protocol
        focused = generate_focused_prompt(concept)
        print(f"Focused: {focused['optimized_prompt']}")

        # Test precision protocol
        precision = generate_precision_prompt(concept)
        print(f"Precision: {precision['precise_prompt']}")

        # Test core protocol
        core = generate_core_prompt(concept)
        print(f"Core: {core}")

        print("=" * 80)

    # Demonstrate community workflow patterns
    print("\nCommunity Workflow Pattern Examples:")
    print("=" * 80)

    workflow_examples = {
        "Character Consistency": "IMG_1 character as mysterious NPC villager in medieval fantasy RPG setting",
        "Spatial Positioning": "place character at center position using IMG_1 chess grid layout",
        "Multi-Reference": "combine IMG_1 character with IMG_2 pose maintaining facial features",
        "Style Transfer": "apply IMG_1 artistic style to character while preserving identity",
        "Object Extraction": "extract sword from IMG_1 and place in medieval armory setting",
        "Scene Blocking": "block scene using IMG_1 composition with enhanced lighting",
        "Lighting Control": "illuminate character with lighting direction from IMG_1",
        "Pose Control": "give character the pose from IMG_1 in castle throne room"
    }

    for workflow_name, example_prompt in workflow_examples.items():
        print(f"{workflow_name}: {example_prompt}")

    print("\n" + "=" * 80)
    print("Protocol Implementation Complete - Ready for RunwayML Gen-4 Deployment")


# Convenience functions for different complexity levels
def generate_comprehensive_prompt(source_concept: Any) -> Dict[str, str]:
    """Generate comprehensive RunwayML prompt with full analysis"""
    generator = ComprehensiveRunwayImageGenerator()
    return generator.generate_runway_prompt(source_concept)

def generate_focused_prompt(image_concept: str) -> Dict[str, str]:
    """Generate focused prompt emphasizing reference integration"""
    generator = ComprehensiveRunwayImageGenerator()

    # Simplified processing for focused approach
    result = generator.generate_runway_prompt(image_concept)

    # Optimize for reference efficiency
    optimized_prompt = generator._optimize_for_references(result["runwayml_image_prompt"])

    return {
        "optimized_prompt": optimized_prompt,
        "reference_plan": result["reference_strategy"],
        "workflow_type": result["workflow_type"]
    }

def generate_precision_prompt(concept: str) -> Dict[str, str]:
    """Generate precision prompt with maximum reference control"""
    generator = ComprehensiveRunwayImageGenerator()

    # Extract core elements only
    core_elements = generator._extract_core_visual_elements(concept)
    reference_type = generator._determine_primary_reference_type(core_elements)

    # Build precise prompt
    precise_prompt = generator._build_precision_prompt(core_elements, reference_type)

    return {
        "precise_prompt": precise_prompt,
        "reference_type": reference_type.value if reference_type else "general"
    }

def generate_core_prompt(input_any: Any) -> str:
    """Generate core essence prompt with maximum efficiency"""
    generator = ComprehensiveRunwayImageGenerator()

    # Distill to absolute essence
    essence = generator._distill_reference_essence(str(input_any))
    composition = generator._optimize_spatial_composition(essence)

    return generator._build_core_prompt(essence, composition)


# Example usage and testing
if __name__ == "__main__":
    # Test the comprehensive generator
    test_concepts = [
        "A mysterious character in a medieval fantasy setting",
        "Modern architectural interior with dramatic lighting",
        "Product design concept for a futuristic device",
        "Character portrait with specific pose and environment"
    ]

    generator = ComprehensiveRunwayImageGenerator()

    for concept in test_concepts:
        print(f"\nInput: {concept}")
        result = generator.generate_runway_prompt(concept)
        print(f"Prompt: {result['runwayml_image_prompt']}")
        print(f"Strategy: {result['reference_strategy']}")
        print(f"Workflow: {result['workflow_type']}")
        print(f"References: {result['reference_requirements']}")
        print("-" * 80)
