#!/usr/bin/env python3
"""
Test suite for sequence execution functionality.

Tests the sequence execution engine including:
- Sequence resolution
- Chain mode execution
- Model integration
- Output formatting
"""

import unittest
import tempfile
import shutil
import os
import sys
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from lvl1_sequence_executor import (
    TemplateCatalog, 
    ExecutorConfig, 
    SequenceManager,
    PromptParser
)


class TestSequenceExecution(unittest.TestCase):
    """Test sequence execution functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_catalog = {
            "templates": {
                "1200-a-test": {
                    "raw": "[Test A] Your goal is not to **describe** but to **transform**. Execute as: `{role=transformer; input=[data:str]; process=[transform()]; output={result:str}}`",
                    "parts": {
                        "title": "Test A",
                        "interpretation": "Your goal is not to **describe** but to **transform**. Execute as:",
                        "transformation": "`{role=transformer; input=[data:str]; process=[transform()]; output={result:str}}`"
                    }
                },
                "1200-b-test": {
                    "raw": "[Test B] Your goal is not to **elaborate** but to **distill**. Execute as: `{role=distiller; input=[data:str]; process=[distill()]; output={result:str}}`",
                    "parts": {
                        "title": "Test B",
                        "interpretation": "Your goal is not to **elaborate** but to **distill**. Execute as:",
                        "transformation": "`{role=distiller; input=[data:str]; process=[distill()]; output={result:str}}`"
                    }
                },
                "1200-c-test": {
                    "raw": "[Test C] Your goal is not to **expand** but to **compress**. Execute as: `{role=compressor; input=[data:str]; process=[compress()]; output={result:str}}`",
                    "parts": {
                        "title": "Test C",
                        "interpretation": "Your goal is not to **expand** but to **compress**. Execute as:",
                        "transformation": "`{role=compressor; input=[data:str]; process=[compress()]; output={result:str}}`"
                    }
                },
                "1200-d-test": {
                    "raw": "[Test D] Your goal is not to **modify** but to **essence**. Execute as: `{role=essencer; input=[data:str]; process=[essence()]; output={result:str}}`",
                    "parts": {
                        "title": "Test D",
                        "interpretation": "Your goal is not to **modify** but to **essence**. Execute as:",
                        "transformation": "`{role=essencer; input=[data:str]; process=[essence()]; output={result:str}}`"
                    }
                }
            },
            "sequences": {
                "1200": [
                    {"template_id": "1200-a-test", "step": "a", "order": 0},
                    {"template_id": "1200-b-test", "step": "b", "order": 1},
                    {"template_id": "1200-c-test", "step": "c", "order": 2},
                    {"template_id": "1200-d-test", "step": "d", "order": 3}
                ]
            }
        }
    
    def test_sequence_resolution(self):
        """Test sequence specification resolution."""
        test_cases = [
            ("1200", 4),  # Full sequence
            ("1200:a", 1),  # Single step
            ("1200:a-c", 3),  # Range
            ("1200:b-d", 3),  # Range from middle
        ]
        
        for sequence_spec, expected_count in test_cases:
            with self.subTest(sequence_spec=sequence_spec):
                steps = SequenceManager.resolve_sequence_specification(
                    self.test_catalog, sequence_spec
                )
                self.assertEqual(len(steps), expected_count,
                               f"Sequence {sequence_spec} should have {expected_count} steps")
    
    def test_prompt_parsing(self):
        """Test prompt parsing with embedded sequence specifications."""
        test_cases = [
            ("[SEQ:1200] Test prompt", "Test prompt", "1200"),
            ("Test prompt [SEQ:1200:a-c]", "Test prompt", "1200:a-c"),
            ("Test prompt --seq=1200", "Test prompt", "1200"),
            ("Regular prompt", "Regular prompt", None),
        ]
        
        for input_prompt, expected_prompt, expected_seq in test_cases:
            with self.subTest(input_prompt=input_prompt):
                cleaned_prompt, sequence_spec = PromptParser.extract_sequence_from_prompt(input_prompt)
                self.assertEqual(cleaned_prompt, expected_prompt)
                self.assertEqual(sequence_spec, expected_seq)
    
    def test_template_validation(self):
        """Test template structure validation."""
        valid_template = self.test_catalog["templates"]["1200-a-test"]
        self.assertTrue(SequenceManager.validate_template(valid_template))
        
        # Test invalid template (missing parts)
        invalid_template = {"raw": "Invalid template"}
        self.assertFalse(SequenceManager.validate_template(invalid_template))
        
        # Test invalid template (missing transformation)
        invalid_template2 = {
            "raw": "Test",
            "parts": {
                "title": "Test",
                "interpretation": "Test"
                # Missing transformation
            }
        }
        self.assertFalse(SequenceManager.validate_template(invalid_template2))
    
    def test_system_instruction_extraction(self):
        """Test system instruction extraction from templates."""
        template = self.test_catalog["templates"]["1200-a-test"]
        
        # Mock the system instruction extractor
        def mock_extractor(template_data):
            if isinstance(template_data, dict) and "parts" in template_data:
                parts = template_data["parts"]
                title = parts.get("title", "")
                interpretation = parts.get("interpretation", "")
                transformation = parts.get("transformation", "")
                return f"# {title}\n\n{interpretation}\n\n{transformation}"
            return template_data.get("raw", "")
        
        instruction = mock_extractor(template)
        
        self.assertIn("Test A", instruction)
        self.assertIn("Your goal is not to", instruction)
        self.assertIn("role=transformer", instruction)
    
    def test_progressive_compression_sequence(self):
        """Test that sequences demonstrate progressive compression."""
        sequence_steps = SequenceManager.resolve_sequence_specification(
            self.test_catalog, "1200"
        )
        
        # Extract the role names from each step
        roles = []
        for step_id, template_data in sequence_steps:
            transformation = template_data["parts"]["transformation"]
            # Extract role from transformation block
            if "role=" in transformation:
                role_start = transformation.find("role=") + 5
                role_end = transformation.find(";", role_start)
                if role_end == -1:
                    role_end = transformation.find(",", role_start)
                if role_end == -1:
                    role_end = transformation.find("}", role_start)
                role = transformation[role_start:role_end].strip()
                roles.append(role)
        
        # Test that roles show progression
        expected_progression = ["transformer", "distiller", "compressor", "essencer"]
        self.assertEqual(roles, expected_progression)
    
    def test_executor_config_validation(self):
        """Test ExecutorConfig validation."""
        # Create a valid config
        sequence_steps = [("a", {"test": "data"})]
        
        config = ExecutorConfig(
            sequence_steps=sequence_steps,
            user_prompt="Test prompt",
            sequence_id="1200",
            models=["gpt-3.5-turbo"],
            system_instruction_extractor=lambda x: "test"
        )
        
        self.assertEqual(config.sequence_id, "1200")
        self.assertEqual(config.user_prompt, "Test prompt")
        self.assertEqual(len(config.sequence_steps), 1)
        self.assertTrue(config.chain_mode)  # Default value
    
    @patch('lvl1_sequence_executor.litellm.acompletion')
    async def test_mock_execution(self, mock_completion):
        """Test sequence execution with mocked LLM calls."""
        # Mock the LLM response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = '{"result": "test output"}'
        mock_completion.return_value = mock_response
        
        # This would test actual execution, but requires more complex setup
        # For now, just test that the mock is properly configured
        self.assertTrue(mock_completion.called == False)  # Not called yet
        
        # Test that we can call the mock
        result = await mock_completion(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "test"}]
        )
        
        self.assertEqual(result.choices[0].message.content, '{"result": "test output"}')


if __name__ == "__main__":
    unittest.main()
