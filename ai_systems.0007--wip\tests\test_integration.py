#!/usr/bin/env python3
"""
Integration tests for the AI Systems 0007 template system.

Tests end-to-end functionality including:
- Template generation to execution pipeline
- Real file system operations
- Catalog generation and loading
- System integration
"""

import unittest
import tempfile
import shutil
import os
import sys
import json
import subprocess
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class TestSystemIntegration(unittest.TestCase):
    """Test complete system integration."""
    
    def setUp(self):
        """Set up test environment with real file structure."""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        
        # Create directory structure
        self.src_dir = Path(self.test_dir) / "src"
        self.templates_dir = self.src_dir / "templates"
        self.stage1_dir = self.templates_dir / "stage1"
        self.generators_dir = self.stage1_dir / "generators"
        self.md_dir = self.stage1_dir / "md"
        
        for directory in [self.src_dir, self.templates_dir, self.stage1_dir, 
                         self.generators_dir, self.md_dir]:
            directory.mkdir(parents=True)
        
        # Copy necessary source files
        original_src = Path(__file__).parent.parent / "src"
        
        # Copy lvl1_sequence_executor.py
        shutil.copy2(
            original_src / "lvl1_sequence_executor.py",
            self.src_dir / "lvl1_sequence_executor.py"
        )
        
        # Copy template system files
        shutil.copytree(
            original_src / "templates",
            self.templates_dir,
            dirs_exist_ok=True
        )
        
        # Change to test directory
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_generator_execution(self):
        """Test that generators can be executed and produce templates."""
        # Create a simple test generator
        generator_content = '''#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# Add parent directories to path
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import TemplateConfig

OUTPUT_DIR = Path(__file__).parent.parent / "md"
CURRENT_STAGE = "stage1"
STAGE_CONFIG = TemplateConfig.STAGES[CURRENT_STAGE]

TEMPLATES = {
    "a-test_integration": {
        "title": "Integration Test A",
        "interpretation": "Your goal is not to **describe** but to **transform** for integration testing. Execute as:",
        "transformation": "`{role=integration_tester; input=[data:str]; process=[test_integration()]; output={result:str}}`",
    },
    "b-test_integration": {
        "title": "Integration Test B", 
        "interpretation": "Your goal is not to **elaborate** but to **distill** for integration testing. Execute as:",
        "transformation": "`{role=integration_distiller; input=[data:str]; process=[distill_test()]; output={result:str}}`",
    }
}

def create_template_files():
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []
    
    for template_key, template in TEMPLATES.items():
        filename = f"9999-{template_key}"  # Use high ID to avoid conflicts
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        
        created_files.append(f"{filename}.md")
    
    return created_files

if __name__ == "__main__":
    files = create_template_files()
    print(f"Created {len(files)} test templates")
    for file in files:
        print(f"  - {file}")
'''
        
        generator_path = self.generators_dir / "test_generator.py"
        generator_path.write_text(generator_content)
        
        # Execute the generator
        result = subprocess.run(
            [sys.executable, str(generator_path)],
            capture_output=True,
            text=True,
            cwd=self.test_dir
        )
        
        self.assertEqual(result.returncode, 0, f"Generator failed: {result.stderr}")
        self.assertIn("Created 2 test templates", result.stdout)
        
        # Check that template files were created
        expected_files = [
            "9999-a-test_integration.md",
            "9999-b-test_integration.md"
        ]
        
        for filename in expected_files:
            filepath = self.md_dir / filename
            self.assertTrue(filepath.exists(), f"Template file {filename} was not created")
            
            # Check file content
            content = filepath.read_text()
            self.assertIn("[Integration Test", content)
            self.assertIn("Your goal is not to", content)
            self.assertIn("`{role=", content)
    
    def test_catalog_generation(self):
        """Test catalog generation from templates."""
        # First create some templates
        self.test_generator_execution()
        
        # Generate catalog
        result = subprocess.run(
            [sys.executable, "src/templates/lvl1_md_to_json.py"],
            capture_output=True,
            text=True,
            cwd=self.test_dir
        )
        
        self.assertEqual(result.returncode, 0, f"Catalog generation failed: {result.stderr}")
        
        # Check that catalog was created
        catalog_path = self.templates_dir / "lvl1.md.templates.json"
        self.assertTrue(catalog_path.exists(), "Catalog file was not created")
        
        # Load and validate catalog
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
        
        self.assertIn("catalog_meta", catalog)
        self.assertIn("templates", catalog)
        self.assertIn("sequences", catalog)
        
        # Check that our test templates are in the catalog
        templates = catalog["templates"]
        self.assertIn("9999-a-test_integration", templates)
        self.assertIn("9999-b-test_integration", templates)
        
        # Check sequence
        sequences = catalog["sequences"]
        self.assertIn("9999", sequences)
        sequence = sequences["9999"]
        self.assertEqual(len(sequence), 2)
    
    def test_sequence_listing(self):
        """Test sequence listing functionality."""
        # Create templates and catalog
        self.test_catalog_generation()
        
        # Test sequence listing
        result = subprocess.run(
            [sys.executable, "src/lvl1_sequence_executor.py", "--list-sequences"],
            capture_output=True,
            text=True,
            cwd=self.test_dir
        )
        
        self.assertEqual(result.returncode, 0, f"Sequence listing failed: {result.stderr}")
        self.assertIn("Available Sequences", result.stdout)
        self.assertIn("9999", result.stdout)
        self.assertIn("Integration Test", result.stdout)
    
    def test_model_listing(self):
        """Test model listing functionality."""
        result = subprocess.run(
            [sys.executable, "src/lvl1_sequence_executor.py", "--list-models"],
            capture_output=True,
            text=True,
            cwd=self.test_dir
        )
        
        self.assertEqual(result.returncode, 0, f"Model listing failed: {result.stderr}")
        self.assertIn("Available Models", result.stdout)
        self.assertIn("OPENAI Models", result.stdout)
        self.assertIn("gpt-", result.stdout)
    
    def test_help_functionality(self):
        """Test help system."""
        result = subprocess.run(
            [sys.executable, "src/lvl1_sequence_executor.py", "--help"],
            capture_output=True,
            text=True,
            cwd=self.test_dir
        )
        
        self.assertEqual(result.returncode, 0, f"Help failed: {result.stderr}")
        self.assertIn("usage:", result.stdout)
        self.assertIn("--sequence", result.stdout)
        self.assertIn("--prompt", result.stdout)
        self.assertIn("--models", result.stdout)
    
    def test_file_structure_integrity(self):
        """Test that the file structure is maintained correctly."""
        # Check that all necessary directories exist
        required_dirs = [
            self.src_dir,
            self.templates_dir,
            self.stage1_dir,
            self.generators_dir,
            self.md_dir
        ]
        
        for directory in required_dirs:
            self.assertTrue(directory.exists(), f"Required directory {directory} does not exist")
        
        # Check that key files exist
        key_files = [
            self.src_dir / "lvl1_sequence_executor.py",
            self.templates_dir / "lvl1_md_to_json.py"
        ]
        
        for file_path in key_files:
            self.assertTrue(file_path.exists(), f"Key file {file_path} does not exist")
    
    def test_stage_configuration_consistency(self):
        """Test that stage configuration is consistent across the system."""
        # Import the configuration
        from templates.lvl1_md_to_json import TemplateConfig
        
        stages = TemplateConfig.STAGES
        
        # Test that stage ranges don't overlap
        ranges = [(info["range"][0], info["range"][1]) for info in stages.values()]
        
        for i, (start1, end1) in enumerate(ranges):
            for j, (start2, end2) in enumerate(ranges):
                if i != j:
                    # Check no overlap
                    self.assertFalse(
                        (start1 <= start2 <= end1) or (start1 <= end2 <= end1) or
                        (start2 <= start1 <= end2) or (start2 <= end1 <= end2),
                        f"Stage ranges overlap: {ranges[i]} and {ranges[j]}"
                    )
        
        # Test that only stage1 has auto_id enabled
        auto_id_stages = [name for name, info in stages.items() if info["auto_id"]]
        self.assertEqual(auto_id_stages, ["stage1"], "Only stage1 should have auto_id enabled")


if __name__ == "__main__":
    unittest.main()
