"""
Specific example test of the RunwayML Gen-4 Image Prompt Generator Protocol
Demonstrating transformation of a complex input into optimized RunwayML prompt
"""

from runway_image_prompt_generator import (
    runway_prompt_protocol_0006a,
    runway_prompt_protocol_0006b,
    runway_prompt_protocol_0006c,
    runway_prompt_protocol_0006d
)

def test_complex_transformation():
    """Test complex input transformation"""
    
    # Complex input example
    complex_input = """
    I want to create an image of a mysterious hooded figure standing in an ancient library, 
    with dramatic lighting coming through tall gothic windows, surrounded by floating magical books, 
    in a photorealistic style but with fantasy elements, making sure the character has a specific 
    pose and the environment has detailed architecture
    """
    
    print("🧪 Complex Input Transformation Test")
    print("=" * 60)
    print(f"Input: {complex_input.strip()}")
    print("=" * 60)
    
    # Test all protocol variants
    protocols = {
        "0006-a (Comprehensive)": runway_prompt_protocol_0006a,
        "0006-b (Focused)": runway_prompt_protocol_0006b,
        "0006-c (Precision)": runway_prompt_protocol_0006c,
        "0006-d (Core)": runway_prompt_protocol_0006d
    }
    
    for protocol_name, protocol_func in protocols.items():
        print(f"\n🔄 Protocol {protocol_name}:")
        print("-" * 40)
        
        try:
            result = protocol_func(complex_input)
            
            if isinstance(result, dict):
                for key, value in result.items():
                    print(f"   {key}: {value}")
            else:
                print(f"   Result: {result}")
                
        except Exception as e:
            print(f"   Error: {e}")
    
    print("\n" + "=" * 60)

def test_community_pattern_examples():
    """Test specific community pattern examples"""
    
    print("\n🎨 Community Pattern Examples")
    print("=" * 60)
    
    # Examples based on actual community workflows
    community_examples = [
        {
            "name": "Character Consistency",
            "input": "Generate a mysterious NPC villager character for a medieval fantasy RPG",
            "expected_workflow": "character_generation"
        },
        {
            "name": "Spatial Positioning", 
            "input": "Place a character at the center of a chess board layout",
            "expected_workflow": "scene_composition"
        },
        {
            "name": "Multi-Reference Blend",
            "input": "Combine the character from one image with the pose from another image",
            "expected_workflow": "character_generation"
        },
        {
            "name": "Architectural Visualization",
            "input": "Modern building interior with dramatic lighting and detailed materials",
            "expected_workflow": "lighting_control"
        }
    ]
    
    for example in community_examples:
        print(f"\n🏗️  {example['name']}:")
        print(f"   Input: {example['input']}")
        
        # Test with comprehensive protocol
        result = runway_prompt_protocol_0006a(example['input'])
        
        print(f"   Prompt: {result['runway_prompt']}")
        print(f"   Strategy: {result['reference_strategy']}")
        
        # Validate character count
        char_count = len(result['runway_prompt'])
        if char_count <= 500:
            print(f"   ✅ Length: {char_count} characters (within limit)")
        else:
            print(f"   ⚠️  Length: {char_count} characters (exceeds limit)")

def test_reference_strategy_detection():
    """Test reference strategy detection"""
    
    print("\n🔗 Reference Strategy Detection")
    print("=" * 60)
    
    strategy_tests = [
        {
            "input": "character portrait with specific lighting",
            "expected_strategy": "character reference"
        },
        {
            "input": "combine multiple elements from different images",
            "expected_strategy": "multi-reference"
        },
        {
            "input": "apply artistic style to realistic subject",
            "expected_strategy": "style transfer"
        },
        {
            "input": "position object at specific location in scene",
            "expected_strategy": "spatial positioning"
        }
    ]
    
    for test in strategy_tests:
        print(f"\n📋 Test: {test['input']}")
        
        result = runway_prompt_protocol_0006a(test['input'])
        strategy = result['reference_strategy'].lower()
        
        print(f"   Strategy: {result['reference_strategy']}")
        
        # Check if expected strategy elements are present
        expected_words = test['expected_strategy'].lower().split()
        if any(word in strategy for word in expected_words):
            print("   ✅ Strategy Detection: Correct")
        else:
            print("   ⚠️  Strategy Detection: Different approach")

def test_motion_term_elimination():
    """Test motion term elimination"""
    
    print("\n🚫 Motion Term Elimination Test")
    print("=" * 60)
    
    motion_inputs = [
        "character running through forest",
        "camera moving through scene",
        "animated sequence with motion blur",
        "video of flying object",
        "dynamic movement and action"
    ]
    
    for motion_input in motion_inputs:
        print(f"\n🏃 Input: {motion_input}")
        
        result = runway_prompt_protocol_0006a(motion_input)
        prompt = result['runway_prompt'].lower()
        
        # Check for motion terms
        motion_terms = ['running', 'moving', 'motion', 'video', 'animated', 'flying', 'movement']
        found_terms = [term for term in motion_terms if term in prompt]
        
        if found_terms:
            print(f"   ⚠️  Motion terms found: {found_terms}")
        else:
            print("   ✅ Motion terms eliminated")
        
        print(f"   Result: {result['runway_prompt']}")

if __name__ == "__main__":
    test_complex_transformation()
    test_community_pattern_examples()
    test_reference_strategy_detection()
    test_motion_term_elimination()
    
    print("\n🎯 All Tests Complete!")
    print("Protocol ready for RunwayML Gen-4 deployment.")
