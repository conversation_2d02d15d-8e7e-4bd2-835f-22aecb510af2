#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.

    "a-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:",
        "transformation": "`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`"
    },

    "b-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **describe** the content, but to **extract** the primary concepts and their main relationships. Execute as:",
        "transformation": "`{role=focused_concept_extractor; input=[content:any]; process=[identify_primary_concepts(), extract_main_relationships(), categorize_concept_types()]; constraints=[focus_on_primary_concepts(), ignore_minor_details()]; requirements=[clear_concept_identification(), relationship_clarity()]; output={concepts:dict}}`"
    },

    "c-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **explain** the content, but to **extract** the essential concepts. Execute as:",
        "transformation": "`{role=essential_concept_extractor; input=[content:any]; process=[isolate_core_concepts(), identify_basic_relationships()]; constraints=[essential_concepts_only()]; requirements=[core_concept_identification()]; output={concepts:dict}}`"
    },

    "d-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **elaborate** but to **identify** core concepts. Execute as:",
        "transformation": "`{role=core_concept_extractor; input=[content:any]; process=[find_central_concepts()]; output={concepts:list}}`"
    },

    # Text Enhancer Sequence (Auto-Generated IDs)
    "a-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **change** the meaning, but to **enhance** the text by improving clarity, flow, and impact while preserving the original message. Execute as:",
        "transformation": "`{role=comprehensive_text_enhancer; input=[text:str]; process=[improve_clarity(), enhance_flow(), strengthen_impact(), refine_word_choice(), optimize_sentence_structure(), maintain_original_meaning()]; constraints=[preserve_core_message(), maintain_author_voice(), improve_readability()]; requirements=[enhanced_clarity(), improved_flow(), stronger_impact()]; output={enhanced_text:str}}`"
    },

    "b-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **rewrite** the text, but to **enhance** its key elements for better clarity and impact. Execute as:",
        "transformation": "`{role=focused_text_enhancer; input=[text:str]; process=[improve_key_clarity_points(), enhance_main_flow(), strengthen_core_impact()]; constraints=[focus_on_major_improvements(), preserve_original_meaning()]; requirements=[clearer_communication(), better_flow()]; output={enhanced_text:str}}`"
    },

    "c-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **alter** the meaning, but to **enhance** essential clarity. Execute as:",
        "transformation": "`{role=essential_text_enhancer; input=[text:str]; process=[improve_core_clarity(), enhance_basic_flow()]; constraints=[essential_improvements_only()]; requirements=[clearer_core_message()]; output={enhanced_text:str}}`"
    },

    "d-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **complicate** but to **clarify** essence. Execute as:",
        "transformation": "`{role=clarity_enhancer; input=[text:str]; process=[maximize_clarity()]; output={clear_text:str}}`"
    },

    # Manual ID Templates: Specify exact ID when needed

    # 1031: Form Classifier Sequence (Progressive Compression)
    # Demonstrates: Comprehensive → Focused → Essential → Absolute

    "a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`"
    },
    
    "b-form_classifier": {
        "title": "Form Classifier", 
        "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:",
        "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`"
    },
    
    "c-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:",
        "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`"
    },
    
    "d-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:",
        "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`"
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage1",
        generator_range=(1000, 1099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
