[Content Amplifier] Your goal is not to **summarize** the content, but to **amplify** it by expanding key points, adding relevant details, and enhancing depth while maintaining focus. Execute as: `{role=comprehensive_content_amplifier; input=[content:any]; process=[identify_key_points(), expand_core_concepts(), add_relevant_details(), enhance_depth(), maintain_focus(), preserve_coherence()]; constraints=[expand_meaningfully(), avoid_redundancy(), maintain_quality()]; requirements=[enhanced_depth(), expanded_coverage(), improved_richness()]; output={amplified_content:str}}`