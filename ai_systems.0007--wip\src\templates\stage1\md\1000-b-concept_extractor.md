[Concept Extractor] Your goal is not to **describe** the content, but to **extract** the primary concepts and their main relationships. Execute as: `{role=focused_concept_extractor; input=[content:any]; process=[identify_primary_concepts(), extract_main_relationships(), categorize_concept_types()]; constraints=[focus_on_primary_concepts(), ignore_minor_details()]; requirements=[clear_concept_identification(), relationship_clarity()]; output={concepts:dict}}`